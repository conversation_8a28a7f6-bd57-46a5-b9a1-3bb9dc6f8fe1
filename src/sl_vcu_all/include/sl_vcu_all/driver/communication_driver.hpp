#ifndef SL_VCU_ALL_DRIVER_COMMUNICATION_DRIVER_HPP_
#define SL_VCU_ALL_DRIVER_COMMUNICATION_DRIVER_HPP_

#include "sl_vcu_all/common/types.hpp"
#include <string>
#include <functional>

namespace sl_vcu_all {
namespace driver {

/**
 * @brief Abstract base class for communication drivers
 * 
 * This class provides the BSP (Board Support Package) interface for low-level
 * communication protocols like SocketCAN, Serial, etc.
 */
class ICommunicationDriver {
public:
    virtual ~ICommunicationDriver() = default;

    /**
     * @brief Initialize the communication driver
     * @param config Configuration string (e.g., "can0" for CAN, "/dev/ttyUSB0" for serial)
     * @return true if initialization successful, false otherwise
     */
    virtual bool initialize(const std::string& config) = 0;

    /**
     * @brief Shutdown the communication driver
     */
    virtual void shutdown() = 0;

    /**
     * @brief Check if the driver is initialized and ready
     * @return true if ready, false otherwise
     */
    virtual bool isReady() const = 0;

    /**
     * @brief Send a frame synchronously
     * @param frame The frame to send
     * @return Result of the operation
     */
    virtual common::AsyncResult sendFrame(const common::CommFrame& frame) = 0;

    /**
     * @brief Send a frame asynchronously
     * @param frame The frame to send
     * @param callback Callback to call when operation completes
     * @param timeout_config Timeout configuration
     */
    virtual void sendFrameAsync(const common::CommFrame& frame, 
                               common::AsyncCallback callback,
                               const common::TimeoutConfig& timeout_config = {}) = 0;

    /**
     * @brief Set callback for received frames
     * @param callback Callback to call when a frame is received
     */
    virtual void setReceiveCallback(std::function<void(const common::CommFrame&)> callback) = 0;

    /**
     * @brief Get driver type identifier
     * @return String identifying the driver type
     */
    virtual std::string getDriverType() const = 0;

    /**
     * @brief Get current configuration
     * @return Configuration string
     */
    virtual std::string getConfiguration() const = 0;
};

/**
 * @brief CAN driver implementation using SocketCAN
 */
class SocketCanDriver : public ICommunicationDriver {
public:
    SocketCanDriver();
    virtual ~SocketCanDriver();

    // ICommunicationDriver interface
    bool initialize(const std::string& config) override;
    void shutdown() override;
    bool isReady() const override;
    common::AsyncResult sendFrame(const common::CommFrame& frame) override;
    void sendFrameAsync(const common::CommFrame& frame, 
                       common::AsyncCallback callback,
                       const common::TimeoutConfig& timeout_config = {}) override;
    void setReceiveCallback(std::function<void(const common::CommFrame&)> callback) override;
    std::string getDriverType() const override;
    std::string getConfiguration() const override;

private:
    class Impl;
    std::unique_ptr<Impl> pImpl_;
};

/**
 * @brief Serial driver implementation for Modbus RTU
 */
class SerialDriver : public ICommunicationDriver {
public:
    SerialDriver();
    virtual ~SerialDriver();

    // ICommunicationDriver interface
    bool initialize(const std::string& config) override;
    void shutdown() override;
    bool isReady() const override;
    common::AsyncResult sendFrame(const common::CommFrame& frame) override;
    void sendFrameAsync(const common::CommFrame& frame, 
                       common::AsyncCallback callback,
                       const common::TimeoutConfig& timeout_config = {}) override;
    void setReceiveCallback(std::function<void(const common::CommFrame&)> callback) override;
    std::string getDriverType() const override;
    std::string getConfiguration() const override;

private:
    class Impl;
    std::unique_ptr<Impl> pImpl_;
};

/**
 * @brief Factory for creating communication drivers
 */
class CommunicationDriverFactory {
public:
    enum class DriverType {
        SOCKET_CAN,
        SERIAL,
        MOCK  // For testing
    };

    static std::unique_ptr<ICommunicationDriver> createDriver(DriverType type);
    static std::unique_ptr<ICommunicationDriver> createDriver(const std::string& type_name);
};

} // namespace driver
} // namespace sl_vcu_all

#endif // SL_VCU_ALL_DRIVER_COMMUNICATION_DRIVER_HPP_
