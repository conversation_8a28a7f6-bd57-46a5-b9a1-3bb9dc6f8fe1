#ifndef SL_VCU_ALL_ROS_NODE_VCU_ROS_NODE_HPP_
#define SL_VCU_ALL_ROS_NODE_VCU_ROS_NODE_HPP_

#include <rclcpp/rclcpp.hpp>
#include <geometry_msgs/msg/twist.hpp>
#include <nav_msgs/msg/odometry.hpp>
#include <sensor_msgs/msg/joint_state.hpp>
#include <sensor_msgs/msg/imu.hpp>
#include <std_msgs/msg/bool.hpp>
#include <tf2_ros/transform_broadcaster.h>

#include "sl_vcu_all/msg/can_frame.hpp"
#include "sl_vcu_all/msg/motor_info.hpp"
#include "sl_vcu_all/msg/motor_state.hpp"
#include "sl_vcu_all/msg/bumper_state.hpp"
#include "sl_vcu_all/srv/led_control.hpp"

#include "sl_vcu_all/application/vcu_application.hpp"
#include "sl_vcu_all/device/device_interface.hpp"
#include "sl_vcu_all/protocol/communication_protocol.hpp"
#include "sl_vcu_all/driver/communication_driver.hpp"

namespace sl_vcu_all {
namespace ros_node {

/**
 * @brief ROS node configuration structure
 */
struct RosNodeConfig {
    // Topic names
    std::string cmd_vel_topic{"cmd_vel"};
    std::string odom_topic{"odom"};
    std::string filtered_odom_topic{""};
    std::string joint_state_topic{"joint_state"};
    std::string can_tx_topic{"can_tx"};
    std::string can_rx_topic{""};
    std::string motor_info_topic{"motor_info"};
    std::string motor_state_topic{"motor_state"};
    std::string bumper_topic{"bumper_state"};
    std::string power_off_topic{"power_off"};
    std::string filtered_imu_topic{"sl_vcu_all/imu_data_filtered"};
    
    // Frame IDs
    std::string odom_frame_id{"odom"};
    std::string base_frame_id{"base_link"};
    
    // Publishing options
    bool publish_tf{true};
    bool publish_motor_info{false};
    bool print_status{false};
    
    // Control timing
    std::chrono::milliseconds control_cycle{20};
    std::chrono::milliseconds status_update_cycle{1000};
    
    // Communication configuration
    std::string driver_type{"socket_can"};
    std::string driver_config{"can0"};
    std::string protocol_type{"canopen_sdo"};
    uint8_t protocol_node_id{1};
    
    // Device types
    std::string motor_device_type{"zl_motor"};
    std::string led_device_type{""};  // Optional
    std::string power_device_type{""};  // Optional
    
    RosNodeConfig() = default;
};

/**
 * @brief Main ROS node class for VCU
 * 
 * This class handles all ROS-specific functionality including publishers,
 * subscribers, timers, and parameter management. It delegates business
 * logic to the application layer.
 */
class VcuRosNode : public rclcpp::Node {
public:
    explicit VcuRosNode(const rclcpp::NodeOptions& options = rclcpp::NodeOptions());
    virtual ~VcuRosNode();

private:
    // ROS parameter management
    void initializeParameters();
    void loadConfiguration();
    
    // Device and application initialization
    bool initializeDevices();
    bool initializeApplication();
    
    // ROS callback methods
    void cmdVelCallback(const geometry_msgs::msg::Twist::SharedPtr msg);
    void bumperCallback(const sl_vcu_all::msg::BumperState::SharedPtr msg);
    void filteredImuCallback(const sensor_msgs::msg::Imu::SharedPtr msg);
    void clearAlarmCallback(const std_msgs::msg::Bool::SharedPtr msg);
    void canRxCallback(const sl_vcu_all::msg::CanFrame::SharedPtr msg);
    
    // Timer callbacks
    void controlTimerCallback();
    void statusTimerCallback();
    
    // Service callbacks
    void ledControlServiceCallback(
        const std::shared_ptr<sl_vcu_all::srv::LedControl::Request> request,
        std::shared_ptr<sl_vcu_all::srv::LedControl::Response> response);
    
    // Application callbacks
    void onStatusUpdate(const application::MotorStatus& status);
    void onOdometryUpdate(const application::OdometryData& odom);
    void onPowerStatusUpdate(const common::PowerStatus& power_status);
    void onApplicationError(const std::string& error_message);
    
    // Publishing methods
    void publishOdometry(const application::OdometryData& odom);
    void publishMotorInfo(const application::MotorStatus& status);
    void publishMotorState(const application::MotorStatus& status);
    void publishJointState(const application::OdometryData& odom);
    void publishPowerStatus(const common::PowerStatus& power_status);
    void publishTransform(const application::OdometryData& odom);
    
    // Utility methods
    double normalizeAngle(double angle);
    geometry_msgs::msg::Quaternion createQuaternionFromYaw(double yaw);
    
    // Configuration
    RosNodeConfig config_;
    application::VcuConfig app_config_;
    
    // Application and devices
    std::unique_ptr<application::VcuApplication> application_;
    std::shared_ptr<driver::ICommunicationDriver> driver_;
    std::shared_ptr<protocol::ICommunicationProtocol> protocol_;
    std::shared_ptr<device::IMotorController> motor_controller_;
    std::shared_ptr<device::ILedController> led_controller_;
    std::shared_ptr<device::IPowerController> power_controller_;
    
    // ROS publishers
    rclcpp::Publisher<nav_msgs::msg::Odometry>::SharedPtr odom_pub_;
    rclcpp::Publisher<sl_vcu_all::msg::CanFrame>::SharedPtr can_tx_pub_;
    rclcpp::Publisher<sl_vcu_all::msg::MotorInfo>::SharedPtr motor_info_pub_;
    rclcpp::Publisher<sl_vcu_all::msg::MotorState>::SharedPtr motor_state_pub_;
    rclcpp::Publisher<std_msgs::msg::Bool>::SharedPtr power_off_pub_;
    rclcpp::Publisher<sensor_msgs::msg::JointState>::SharedPtr joint_state_pub_;
    
    // ROS subscribers
    rclcpp::Subscription<geometry_msgs::msg::Twist>::SharedPtr cmd_vel_sub_;
    rclcpp::Subscription<sl_vcu_all::msg::CanFrame>::SharedPtr can_rx_sub_;
    rclcpp::Subscription<sl_vcu_all::msg::BumperState>::SharedPtr bumper_sub_;
    rclcpp::Subscription<sensor_msgs::msg::Imu>::SharedPtr filtered_imu_sub_;
    rclcpp::Subscription<std_msgs::msg::Bool>::SharedPtr alarm_clear_sub_;
    
    // ROS services
    rclcpp::Service<sl_vcu_all::srv::LedControl>::SharedPtr led_control_service_;
    
    // ROS timers
    rclcpp::TimerBase::SharedPtr control_timer_;
    rclcpp::TimerBase::SharedPtr status_timer_;
    
    // TF broadcaster
    std::unique_ptr<tf2_ros::TransformBroadcaster> tf_broadcaster_;
    
    // State tracking
    rclcpp::Time last_cmd_vel_time_;
    rclcpp::Time last_bumper_time_;
    rclcpp::Time last_odom_time_;
    
    // IMU integration
    struct ImuYawSample {
        rclcpp::Time timestamp;
        double yaw;
    };
    std::deque<ImuYawSample> imu_yaw_buffer_;
    std::mutex imu_buffer_mutex_;
    int imu_time_offset_ms_{6};
    
    bool getImuYawAtTime(const rclcpp::Time& target_time, double& yaw);
};

} // namespace ros_node
} // namespace sl_vcu_all

#endif // SL_VCU_ALL_ROS_NODE_VCU_ROS_NODE_HPP_
