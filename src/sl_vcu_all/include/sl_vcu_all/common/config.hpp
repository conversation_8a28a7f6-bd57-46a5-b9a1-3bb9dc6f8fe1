#ifndef SL_VCU_ALL_COMMON_CONFIG_HPP_
#define SL_VCU_ALL_COMMON_CONFIG_HPP_

#include "sl_vcu_all/common/types.hpp"
#include "sl_vcu_all/application/vcu_application.hpp"
#include "sl_vcu_all/ros_node/vcu_ros_node.hpp"
#include <string>
#include <map>
#include <variant>

namespace sl_vcu_all {
namespace common {

/**
 * @brief Configuration value type
 */
using ConfigValue = std::variant<bool, int, double, std::string>;

/**
 * @brief Configuration manager for the layered architecture
 */
class ConfigManager {
public:
    ConfigManager() = default;
    ~ConfigManager() = default;

    /**
     * @brief Load configuration from a map (typically from ROS parameters)
     * @param config_map Map of configuration key-value pairs
     */
    void loadFromMap(const std::map<std::string, ConfigValue>& config_map);

    /**
     * @brief Get a configuration value
     * @param key Configuration key
     * @param default_value Default value if key not found
     * @return Configuration value
     */
    template<typename T>
    T getValue(const std::string& key, const T& default_value) const;

    /**
     * @brief Set a configuration value
     * @param key Configuration key
     * @param value Configuration value
     */
    template<typename T>
    void setValue(const std::string& key, const T& value);

    /**
     * @brief Check if a configuration key exists
     * @param key Configuration key
     * @return true if key exists, false otherwise
     */
    bool hasKey(const std::string& key) const;

    /**
     * @brief Get all configuration keys
     * @return Vector of all configuration keys
     */
    std::vector<std::string> getKeys() const;

    /**
     * @brief Create ROS node configuration from loaded config
     * @return ROS node configuration
     */
    ros_node::RosNodeConfig createRosNodeConfig() const;

    /**
     * @brief Create application configuration from loaded config
     * @return Application configuration
     */
    application::VcuConfig createApplicationConfig() const;

    /**
     * @brief Create robot parameters from loaded config
     * @return Robot parameters
     */
    RobotParameters createRobotParameters() const;

private:
    std::map<std::string, ConfigValue> config_map_;
};

/**
 * @brief Configuration keys namespace
 */
namespace config_keys {
    // Communication parameters
    constexpr const char* CAN_ID_TX = "can_id_tx";
    constexpr const char* CAN_ID_RX = "can_id_rx";
    constexpr const char* DRIVER_TYPE = "driver_type";
    constexpr const char* DRIVER_CONFIG = "driver_config";
    constexpr const char* PROTOCOL_TYPE = "protocol_type";
    constexpr const char* PROTOCOL_NODE_ID = "protocol_node_id";

    // Robot physical parameters
    constexpr const char* WHEEL_DIAMETER_LEFT = "wheel_diameter_left";
    constexpr const char* WHEEL_DIAMETER_RIGHT = "wheel_diameter_right";
    constexpr const char* WHEEL_SEPARATION = "wheel_separation";
    constexpr const char* GEAR_RATIO = "gear_ratio";
    constexpr const char* ENCODER_RESOLUTION = "encoder_resolution";

    // Frame IDs
    constexpr const char* ODOM_FRAME_ID = "odom_frame_id";
    constexpr const char* BASE_FRAME_ID = "base_frame_id";
    constexpr const char* PUBLISH_TF = "publish_tf";

    // Topic names
    constexpr const char* CMD_VEL_TOPIC = "cmd_vel_topic";
    constexpr const char* ODOM_TOPIC = "odom_topic";
    constexpr const char* FILTERED_ODOM_TOPIC = "filtered_odom_topic";
    constexpr const char* JOINT_STATE_TOPIC = "joint_state_topic";
    constexpr const char* CAN_TX_TOPIC = "can_tx_topic";
    constexpr const char* CAN_RX_TOPIC = "can_rx_topic";
    constexpr const char* MOTOR_INFO_TOPIC = "motor_info_topic";
    constexpr const char* MOTOR_STATE_TOPIC = "motor_state_topic";
    constexpr const char* BUMPER_TOPIC = "bumper_topic";
    constexpr const char* POWER_OFF_TOPIC = "power_off_topic";
    constexpr const char* FILTERED_IMU_TOPIC = "filtered_imu_topic";

    // Control timing parameters
    constexpr const char* CONTROL_CYCLE_MS = "control_cycle_ms";
    constexpr const char* STATUS_UPDATE_CYCLE_MS = "status_update_cycle_ms";
    constexpr const char* PRINT_STATUS_OUT = "print_status_out";

    // Timeout parameters
    constexpr const char* CMD_VEL_TIMEOUT_MS = "cmd_vel_timeout_ms";
    constexpr const char* BUMPER_TIMEOUT_MS = "bumper_timeout_ms";
    constexpr const char* SDO_RESPONSE_TIMEOUT_MS = "sdo_response_timeout_ms";

    // Publishing options
    constexpr const char* PUBLISH_MOTOR_INFO = "publish_motor_info";

    // CAN communication options
    constexpr const char* USE_SOCKCAN_DIRECT = "use_sockcan_direct";
    constexpr const char* CAN_INTERFACE = "can_interface";
    constexpr const char* MIN_SEND_INTERVAL_MS = "min_send_interval_ms";

    // GPIO parameters
    constexpr const char* EMERGENCY_STOP_BIT0_TRIGGER_LEVEL = "emergency_stop_bit0_trigger_level";

    // Power off control parameters
    constexpr const char* POWER_OFF_TIMEOUT_MS = "power_off_timeout_ms";
    constexpr const char* POWER_OFF_PUBLISH_PERIOD_MS = "power_off_publish_period_ms";

    // IMU parameters
    constexpr const char* IMU_TIME_OFFSET_MS = "imu_time_offset_ms";

    // Device types
    constexpr const char* MOTOR_DEVICE_TYPE = "motor_device_type";
    constexpr const char* LED_DEVICE_TYPE = "led_device_type";
    constexpr const char* POWER_DEVICE_TYPE = "power_device_type";
}

// Template implementations
template<typename T>
T ConfigManager::getValue(const std::string& key, const T& default_value) const {
    auto it = config_map_.find(key);
    if (it != config_map_.end()) {
        try {
            return std::get<T>(it->second);
        } catch (const std::bad_variant_access&) {
            return default_value;
        }
    }
    return default_value;
}

template<typename T>
void ConfigManager::setValue(const std::string& key, const T& value) {
    config_map_[key] = value;
}

} // namespace common
} // namespace sl_vcu_all

#endif // SL_VCU_ALL_COMMON_CONFIG_HPP_
