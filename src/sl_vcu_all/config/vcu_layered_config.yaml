# VCU Layered Architecture Configuration
# This file contains configuration for the new layered VCU architecture

vcu_ros_node:
  ros__parameters:
    # ========================================
    # Layer 5: Driver Layer Configuration
    # ========================================
    driver_type: "socket_can"              # Options: socket_can, serial, mock
    driver_config: "can0"                  # For socket_can: interface name, for serial: device path
    
    # ========================================
    # Layer 4: Protocol Layer Configuration
    # ========================================
    protocol_type: "canopen_sdo"           # Options: canopen_sdo, modbus_rtu, mock
    protocol_node_id: 1                    # Node ID for the protocol (CAN node ID or Modbus slave ID)
    
    # Protocol-specific timeouts
    sdo_response_timeout_ms: 1000          # SDO response timeout in milliseconds
    
    # ========================================
    # Layer 3: Device Layer Configuration
    # ========================================
    motor_device_type: "zl_motor"          # Options: zl_motor, mock
    led_device_type: ""                    # Options: zl_led, mock (empty = disabled)
    power_device_type: "zl_power"          # Options: zl_power, mock (empty = disabled)
    
    # ========================================
    # Layer 2: Application Layer Configuration
    # ========================================
    
    # Robot physical parameters
    wheel_diameter_left: 0.1388            # Left wheel diameter in meters
    wheel_diameter_right: 0.140            # Right wheel diameter in meters
    wheel_separation: 0.390                # Distance between wheels in meters
    gear_ratio: 1.0                        # Motor gear ratio
    encoder_resolution: 16384.0            # Encoder ticks per revolution
    
    # Control timing parameters
    control_cycle_ms: 20                   # Main control loop cycle time in milliseconds
    status_update_cycle_ms: 1000           # Status update cycle for temperatures and currents
    
    # Timeout parameters
    cmd_vel_timeout_ms: 500                # Command velocity timeout in milliseconds
    bumper_timeout_ms: 5000                # Bumper state timeout in milliseconds
    
    # Safety parameters
    emergency_stop_bit0_trigger_level: 0   # 0 = low level triggers emergency stop, 1 = high level
    
    # Power control parameters
    power_off_timeout_ms: 5000             # Power off timeout in milliseconds
    power_off_publish_period_ms: 100       # Power off topic publish period in milliseconds
    
    # ========================================
    # Layer 1: ROS Node Layer Configuration
    # ========================================
    
    # Frame IDs for TF and odometry
    odom_frame_id: "odom"                  # Odometry frame ID
    base_frame_id: "base_link"             # Base frame ID
    publish_tf: true                       # Whether to publish TF transforms
    
    # Topic names
    cmd_vel_topic: "cmd_vel"               # Command velocity topic
    odom_topic: "odom"                     # Odometry topic
    filtered_odom_topic: ""                # Filtered odometry topic (optional)
    joint_state_topic: "joint_state"      # Joint state topic
    can_tx_topic: "can_tx"                 # CAN transmit topic (when not using direct sockcan)
    can_rx_topic: ""                       # CAN receive topic (auto-generated if empty)
    motor_info_topic: "motor_info"         # Motor information topic
    motor_state_topic: "motor_state"       # Motor state topic
    bumper_topic: "bumper_state"           # Bumper state topic
    power_off_topic: "power_off"           # Power off topic
    filtered_imu_topic: "sl_vcu_all/imu_data_filtered"  # Filtered IMU topic
    
    # Publishing options
    publish_motor_info: false              # Whether to publish motor info messages
    print_status_out: false                # Whether to print status to console
    
    # IMU integration parameters
    imu_time_offset_ms: 6                  # How much milliseconds IMU messages are ahead of odometry
    
    # ========================================
    # Legacy Compatibility Parameters
    # ========================================
    # These parameters are kept for backward compatibility with existing launch files
    
    # CAN communication parameters (mapped to driver/protocol config)
    can_id_tx: 0x601                       # CAN ID for transmitting commands (protocol layer)
    can_id_rx: 0x581                       # CAN ID for receiving responses (protocol layer)
    
    # CAN communication options (mapped to driver config)
    use_sockcan_direct: true               # Use direct SocketCAN instead of ROS topics
    can_interface: "can0"                  # SocketCAN interface name
    min_send_interval_ms: 2                # Minimum interval between CAN frame sends
    
    # ========================================
    # Configuration Mapping Rules
    # ========================================
    # The following rules apply for parameter mapping:
    #
    # 1. Driver Configuration:
    #    - If use_sockcan_direct=true: driver_type="socket_can", driver_config=can_interface
    #    - If use_sockcan_direct=false: driver_type="mock" (uses ROS topics)
    #
    # 2. Protocol Configuration:
    #    - protocol_type="canopen_sdo" for CAN-based communication
    #    - protocol_node_id derived from can_id_tx (can_id_tx - 0x600)
    #
    # 3. Device Configuration:
    #    - motor_device_type="zl_motor" for ZL motor controllers
    #    - power_device_type="zl_power" if power control is needed
    #
    # 4. Application Configuration:
    #    - All robot physical parameters map directly
    #    - All timeout parameters map directly
    #    - All safety parameters map directly
    #
    # 5. ROS Node Configuration:
    #    - All topic names map directly
    #    - All frame IDs map directly
    #    - All publishing options map directly
