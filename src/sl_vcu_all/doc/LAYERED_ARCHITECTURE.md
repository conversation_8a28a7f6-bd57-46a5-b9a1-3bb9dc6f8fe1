# VCU Layered Architecture Documentation

## Overview

This document describes the new layered architecture for the VCU (Vehicle Control Unit) system. The architecture is designed with clear separation of concerns, making it easy to swap out different devices, protocols, and communication drivers without changing the upper layers.

## Architecture Layers

The system is organized into 5 distinct layers, from top to bottom:

### Layer 1: ROS Node Layer (`src/ros_node/`)
**Responsibility**: ROS-specific functionality
- Publishers, subscribers, timers
- Parameter management
- Service servers
- TF broadcasting
- Message conversions between ROS and application types

**Key Classes**:
- `VcuRosNode`: Main ROS node implementation
- Configuration loading and parameter management
- ROS message publishing and subscription

### Layer 2: Application Layer (`src/application/`)
**Responsibility**: Business logic without ROS dependencies
- Motor control logic
- LED control logic
- Power management
- Safety constraints (bumper, emergency stop)
- Odometry calculation
- State machine management

**Key Classes**:
- `VcuApplication`: Main application controller
- `VelocityCommand`, `BumperState`, `OdometryData`: Application data types
- `ApplicationState`: State machine enumeration

### Layer 3: Device Layer (`src/device/`)
**Responsibility**: Hardware device abstraction
- Motor controller interface and implementations
- LED controller interface and implementations
- Power controller interface and implementations
- Device-specific command translation

**Key Classes**:
- `IMotorController`: Motor controller interface
- `ILedController`: LED controller interface
- `IPowerController`: Power controller interface
- `ZlMotorController`: ZL motor controller implementation
- `ZlPowerController`: ZL power controller implementation

### Layer 4: Protocol Layer (`src/protocol/`)
**Responsibility**: Communication protocol implementation
- CANopen SDO protocol
- Modbus RTU protocol
- Protocol-specific frame formatting
- Request/response matching
- Timeout handling

**Key Classes**:
- `ICommunicationProtocol`: Protocol interface
- `CanopenSdoProtocol`: CANopen SDO implementation
- `ModbusRtuProtocol`: Modbus RTU implementation
- `CommunicationProtocolFactory`: Protocol factory

### Layer 5: Driver Layer (`src/driver/`)
**Responsibility**: Low-level communication (BSP interface)
- SocketCAN driver
- Serial driver
- Frame transmission and reception
- Hardware-specific communication

**Key Classes**:
- `ICommunicationDriver`: Driver interface
- `SocketCanDriver`: SocketCAN implementation
- `SerialDriver`: Serial communication implementation
- `CommunicationDriverFactory`: Driver factory

## Async Communication and Timeout Handling

### Async Patterns
All layers implement asynchronous communication patterns:

1. **Callback-based**: Functions accept callbacks that are called when operations complete
2. **Future-based**: Operations return immediately and notify completion via callbacks
3. **Event-driven**: Layers communicate through events and callbacks

### Timeout Handling
Comprehensive timeout handling is implemented at multiple levels:

1. **Driver Level**: Send/receive timeouts for low-level communication
2. **Protocol Level**: Request/response timeouts with automatic cleanup
3. **Device Level**: Command execution timeouts
4. **Application Level**: High-level operation timeouts (cmd_vel, bumper, etc.)

### Example Async Flow
```cpp
// Application layer requests motor speed
application_->setMotorSpeeds(speeds, [](const AsyncResult& result) {
    if (result.isSuccess()) {
        // Handle success
    } else {
        // Handle error or timeout
    }
});

// This flows down through:
// Application -> Device -> Protocol -> Driver
// Each layer adds its own timeout and error handling
```

## Configuration Architecture

### Layered Configuration
Configuration is organized by layer responsibility:

```yaml
vcu_ros_node:
  ros__parameters:
    # Layer 5: Driver configuration
    driver_type: "socket_can"
    driver_config: "can0"
    
    # Layer 4: Protocol configuration
    protocol_type: "canopen_sdo"
    protocol_node_id: 1
    
    # Layer 3: Device configuration
    motor_device_type: "zl_motor"
    led_device_type: ""
    power_device_type: "zl_power"
    
    # Layer 2: Application configuration
    wheel_diameter_left: 0.1388
    wheel_separation: 0.390
    cmd_vel_timeout_ms: 500
    
    # Layer 1: ROS node configuration
    odom_frame_id: "odom"
    cmd_vel_topic: "cmd_vel"
    publish_tf: true
```

### Configuration Manager
The `ConfigManager` class provides:
- Type-safe parameter access
- Default value handling
- Configuration validation
- Layer-specific configuration creation

## Benefits of the Layered Architecture

### 1. Modularity
- Each layer has a single responsibility
- Clear interfaces between layers
- Easy to test individual layers

### 2. Reusability
- Layers 1 and 2 can be reused with different devices
- Protocol and driver layers can be swapped independently
- Device implementations can be reused across different applications

### 3. Maintainability
- Changes in one layer don't affect others
- Clear separation of concerns
- Well-defined interfaces

### 4. Testability
- Each layer can be unit tested independently
- Mock implementations for testing
- Clear dependency injection points

### 5. Flexibility
- Easy to add new devices (implement device interface)
- Easy to add new protocols (implement protocol interface)
- Easy to add new communication methods (implement driver interface)

## Usage Examples

### Adding a New Device
1. Implement the appropriate device interface (`IMotorController`, `ILedController`, etc.)
2. Add device creation to the factory
3. Update configuration to specify the new device type
4. No changes needed in upper layers

### Adding a New Protocol
1. Implement `ICommunicationProtocol` interface
2. Add protocol creation to the factory
3. Update configuration to specify the new protocol type
4. No changes needed in device or application layers

### Adding a New Communication Driver
1. Implement `ICommunicationDriver` interface
2. Add driver creation to the factory
3. Update configuration to specify the new driver type
4. No changes needed in upper layers

## Migration from Legacy Code

The new architecture maintains backward compatibility through:

1. **Parameter mapping**: Legacy parameters are mapped to new layer-specific parameters
2. **Topic compatibility**: Same ROS topics and message types
3. **Behavior preservation**: Same external behavior and interfaces
4. **Gradual migration**: Can be deployed alongside existing code

## Future Enhancements

The layered architecture enables easy addition of:

1. **New motor controllers**: Implement `IMotorController` interface
2. **New communication protocols**: Implement `ICommunicationProtocol` interface
3. **New safety features**: Add to application layer
4. **Advanced control algorithms**: Enhance application layer
5. **Diagnostic capabilities**: Add monitoring at each layer
6. **Configuration validation**: Enhance `ConfigManager`
7. **Performance monitoring**: Add metrics collection at each layer

## Conclusion

The layered architecture provides a robust, maintainable, and extensible foundation for the VCU system. It enables easy adaptation to different hardware configurations while maintaining clean separation of concerns and comprehensive error handling.
