#include "sl_vcu_all/protocol/communication_protocol.hpp"
#include <map>
#include <mutex>
#include <chrono>
#include <thread>
#include <atomic>

namespace sl_vcu_all {
namespace protocol {

// Modbus function codes
namespace modbus_functions {
    constexpr uint8_t READ_HOLDING_REGISTERS = 0x03;
    constexpr uint8_t READ_INPUT_REGISTERS = 0x04;
    constexpr uint8_t WRITE_SINGLE_REGISTER = 0x06;
    constexpr uint8_t WRITE_MULTIPLE_REGISTERS = 0x10;
    
    constexpr uint8_t ERROR_FLAG = 0x80;
}

class ModbusRtuProtocol::Impl {
public:
    struct PendingRequest {
        uint16_t address;
        uint8_t sub_address;
        common::AsyncCallback callback;
        std::chrono::steady_clock::time_point timestamp;
        std::chrono::milliseconds timeout;
        bool is_read;
        uint8_t transaction_id;
    };

    Impl() : node_id_(1), initialized_(false), next_transaction_id_(1) {}

    ~Impl() {
        shutdown();
    }

    bool initialize(std::shared_ptr<driver::ICommunicationDriver> driver) {
        if (initialized_) {
            return true;
        }

        driver_ = driver;
        if (!driver_ || !driver_->isReady()) {
            return false;
        }

        // Set up receive callback
        driver_->setReceiveCallback([this](const common::CommFrame& frame) {
            handleReceivedFrame(frame);
        });

        // Start timeout checking thread
        running_ = true;
        timeout_thread_ = std::thread(&Impl::timeoutThreadFunction, this);

        initialized_ = true;
        return true;
    }

    void shutdown() {
        if (!initialized_) {
            return;
        }

        running_ = false;
        if (timeout_thread_.joinable()) {
            timeout_thread_.join();
        }

        // Clear pending requests
        {
            std::lock_guard<std::mutex> lock(pending_requests_mutex_);
            for (auto& pair : pending_requests_) {
                if (pair.second.callback) {
                    pair.second.callback(common::AsyncResult(common::ResultCode::UNKNOWN_ERROR, "Protocol shutdown"));
                }
            }
            pending_requests_.clear();
        }

        driver_.reset();
        initialized_ = false;
    }

    bool isReady() const {
        return initialized_ && driver_ && driver_->isReady();
    }

    void setNodeId(uint8_t node_id) {
        node_id_ = node_id;
    }

    uint8_t getNodeId() const {
        return node_id_;
    }

    common::AsyncResult readRegister(uint16_t address, uint8_t sub_address, uint32_t& data) {
        if (!isReady()) {
            return common::AsyncResult(common::ResultCode::DEVICE_NOT_READY, "Protocol not ready");
        }

        // Create Modbus RTU read holding registers frame
        common::CommFrame frame;
        frame.length = 8;  // 1 + 1 + 2 + 2 + 2 (slave_id + function + address + count + crc)

        frame.data[0] = node_id_;
        frame.data[1] = modbus_functions::READ_HOLDING_REGISTERS;
        frame.data[2] = static_cast<uint8_t>((address >> 8) & 0xFF);
        frame.data[3] = static_cast<uint8_t>(address & 0xFF);
        frame.data[4] = 0x00;  // Read 1 register (high byte)
        frame.data[5] = 0x01;  // Read 1 register (low byte)
        
        // Calculate CRC16 (simplified - in real implementation use proper CRC16)
        uint16_t crc = calculateCrc16(frame.data, 6);
        frame.data[6] = static_cast<uint8_t>(crc & 0xFF);
        frame.data[7] = static_cast<uint8_t>((crc >> 8) & 0xFF);

        // Send frame synchronously
        auto result = driver_->sendFrame(frame);
        if (!result.isSuccess()) {
            return result;
        }

        // Wait for response (simplified synchronous implementation)
        std::this_thread::sleep_for(std::chrono::milliseconds(100));
        
        // For now, return success with dummy data
        data = 0;
        return common::AsyncResult(common::ResultCode::SUCCESS);
    }

    common::AsyncResult writeRegister(uint16_t address, uint8_t sub_address, uint32_t data) {
        if (!isReady()) {
            return common::AsyncResult(common::ResultCode::DEVICE_NOT_READY, "Protocol not ready");
        }

        // Create Modbus RTU write single register frame
        common::CommFrame frame;
        frame.length = 8;  // 1 + 1 + 2 + 2 + 2 (slave_id + function + address + data + crc)

        frame.data[0] = node_id_;
        frame.data[1] = modbus_functions::WRITE_SINGLE_REGISTER;
        frame.data[2] = static_cast<uint8_t>((address >> 8) & 0xFF);
        frame.data[3] = static_cast<uint8_t>(address & 0xFF);
        frame.data[4] = static_cast<uint8_t>((data >> 8) & 0xFF);
        frame.data[5] = static_cast<uint8_t>(data & 0xFF);
        
        // Calculate CRC16
        uint16_t crc = calculateCrc16(frame.data, 6);
        frame.data[6] = static_cast<uint8_t>(crc & 0xFF);
        frame.data[7] = static_cast<uint8_t>((crc >> 8) & 0xFF);

        // Send frame synchronously
        return driver_->sendFrame(frame);
    }

    void readRegisterAsync(uint16_t address, uint8_t sub_address,
                          common::AsyncCallback callback,
                          const common::TimeoutConfig& timeout_config) {
        if (!isReady()) {
            if (callback) {
                callback(common::AsyncResult(common::ResultCode::DEVICE_NOT_READY, "Protocol not ready"));
            }
            return;
        }

        uint8_t transaction_id = next_transaction_id_++;

        // Add to pending requests
        {
            std::lock_guard<std::mutex> lock(pending_requests_mutex_);
            PendingRequest request;
            request.address = address;
            request.sub_address = sub_address;
            request.callback = callback;
            request.timestamp = std::chrono::steady_clock::now();
            request.timeout = timeout_config.timeout;
            request.is_read = true;
            request.transaction_id = transaction_id;
            pending_requests_[transaction_id] = request;
        }

        // Create and send Modbus RTU read frame
        common::CommFrame frame;
        frame.length = 8;

        frame.data[0] = node_id_;
        frame.data[1] = modbus_functions::READ_HOLDING_REGISTERS;
        frame.data[2] = static_cast<uint8_t>((address >> 8) & 0xFF);
        frame.data[3] = static_cast<uint8_t>(address & 0xFF);
        frame.data[4] = 0x00;  // Read 1 register (high byte)
        frame.data[5] = 0x01;  // Read 1 register (low byte)
        
        uint16_t crc = calculateCrc16(frame.data, 6);
        frame.data[6] = static_cast<uint8_t>(crc & 0xFF);
        frame.data[7] = static_cast<uint8_t>((crc >> 8) & 0xFF);

        driver_->sendFrameAsync(frame, [this, transaction_id](const common::AsyncResult& result) {
            if (!result.isSuccess()) {
                // Remove from pending and call callback with error
                std::lock_guard<std::mutex> lock(pending_requests_mutex_);
                auto it = pending_requests_.find(transaction_id);
                if (it != pending_requests_.end()) {
                    if (it->second.callback) {
                        it->second.callback(result);
                    }
                    pending_requests_.erase(it);
                }
            }
            // If successful, wait for response in handleReceivedFrame
        }, timeout_config);
    }

    void writeRegisterAsync(uint16_t address, uint8_t sub_address, uint32_t data,
                           common::AsyncCallback callback,
                           const common::TimeoutConfig& timeout_config) {
        if (!isReady()) {
            if (callback) {
                callback(common::AsyncResult(common::ResultCode::DEVICE_NOT_READY, "Protocol not ready"));
            }
            return;
        }

        uint8_t transaction_id = next_transaction_id_++;

        // Add to pending requests
        {
            std::lock_guard<std::mutex> lock(pending_requests_mutex_);
            PendingRequest request;
            request.address = address;
            request.sub_address = sub_address;
            request.callback = callback;
            request.timestamp = std::chrono::steady_clock::now();
            request.timeout = timeout_config.timeout;
            request.is_read = false;
            request.transaction_id = transaction_id;
            pending_requests_[transaction_id] = request;
        }

        // Create and send Modbus RTU write frame
        common::CommFrame frame;
        frame.length = 8;

        frame.data[0] = node_id_;
        frame.data[1] = modbus_functions::WRITE_SINGLE_REGISTER;
        frame.data[2] = static_cast<uint8_t>((address >> 8) & 0xFF);
        frame.data[3] = static_cast<uint8_t>(address & 0xFF);
        frame.data[4] = static_cast<uint8_t>((data >> 8) & 0xFF);
        frame.data[5] = static_cast<uint8_t>(data & 0xFF);
        
        uint16_t crc = calculateCrc16(frame.data, 6);
        frame.data[6] = static_cast<uint8_t>(crc & 0xFF);
        frame.data[7] = static_cast<uint8_t>((crc >> 8) & 0xFF);

        driver_->sendFrameAsync(frame, [this, transaction_id](const common::AsyncResult& result) {
            if (!result.isSuccess()) {
                // Remove from pending and call callback with error
                std::lock_guard<std::mutex> lock(pending_requests_mutex_);
                auto it = pending_requests_.find(transaction_id);
                if (it != pending_requests_.end()) {
                    if (it->second.callback) {
                        it->second.callback(result);
                    }
                    pending_requests_.erase(it);
                }
            }
            // If successful, wait for response in handleReceivedFrame
        }, timeout_config);
    }

    std::string getProtocolType() const {
        return "Modbus RTU";
    }

private:
    void handleReceivedFrame(const common::CommFrame& frame) {
        // Basic Modbus RTU response parsing
        if (frame.length < 5 || frame.data[0] != node_id_) {
            return;
        }

        uint8_t function_code = frame.data[1];
        
        // Check for error response
        if (function_code & modbus_functions::ERROR_FLAG) {
            uint8_t error_code = frame.data[2];
            // Handle error response - for simplicity, we'll just log it
            return;
        }

        // For simplicity, we'll match responses to the first pending request
        // In a real implementation, you would need better request/response matching
        std::lock_guard<std::mutex> lock(pending_requests_mutex_);
        if (pending_requests_.empty()) {
            return;
        }

        auto it = pending_requests_.begin();
        PendingRequest request = it->second;
        pending_requests_.erase(it);

        // Process response based on function code
        common::AsyncResult result;
        if (function_code == modbus_functions::READ_HOLDING_REGISTERS && request.is_read) {
            if (frame.length >= 5) {
                uint8_t byte_count = frame.data[2];
                if (byte_count >= 2 && frame.length >= 5 + byte_count) {
                    uint32_t data = (static_cast<uint32_t>(frame.data[3]) << 8) | frame.data[4];
                    result = common::AsyncResult(common::ResultCode::SUCCESS, "", data);
                } else {
                    result = common::AsyncResult(common::ResultCode::PROTOCOL_ERROR, "Invalid response length");
                }
            } else {
                result = common::AsyncResult(common::ResultCode::PROTOCOL_ERROR, "Response too short");
            }
        } else if (function_code == modbus_functions::WRITE_SINGLE_REGISTER && !request.is_read) {
            result = common::AsyncResult(common::ResultCode::SUCCESS);
        } else {
            result = common::AsyncResult(common::ResultCode::PROTOCOL_ERROR, "Unexpected response");
        }

        // Call callback
        if (request.callback) {
            request.callback(result);
        }
    }

    void timeoutThreadFunction() {
        while (running_) {
            std::this_thread::sleep_for(std::chrono::milliseconds(100));

            std::vector<PendingRequest> timed_out_requests;
            auto now = std::chrono::steady_clock::now();

            {
                std::lock_guard<std::mutex> lock(pending_requests_mutex_);
                auto it = pending_requests_.begin();
                while (it != pending_requests_.end()) {
                    auto elapsed = std::chrono::duration_cast<std::chrono::milliseconds>(now - it->second.timestamp);
                    if (elapsed > it->second.timeout) {
                        timed_out_requests.push_back(it->second);
                        it = pending_requests_.erase(it);
                    } else {
                        ++it;
                    }
                }
            }

            // Call timeout callbacks outside of lock
            for (const auto& request : timed_out_requests) {
                if (request.callback) {
                    request.callback(common::AsyncResult(common::ResultCode::TIMEOUT, "Modbus request timeout"));
                }
            }
        }
    }

    uint16_t calculateCrc16(const uint8_t* data, size_t length) {
        // Simplified CRC16 calculation for Modbus RTU
        // In a real implementation, use proper CRC16-IBM/ANSI calculation
        uint16_t crc = 0xFFFF;
        for (size_t i = 0; i < length; i++) {
            crc ^= data[i];
            for (int j = 0; j < 8; j++) {
                if (crc & 0x0001) {
                    crc = (crc >> 1) ^ 0xA001;
                } else {
                    crc >>= 1;
                }
            }
        }
        return crc;
    }

    std::shared_ptr<driver::ICommunicationDriver> driver_;
    uint8_t node_id_;
    std::atomic<bool> initialized_;
    std::atomic<bool> running_;
    std::atomic<uint8_t> next_transaction_id_;

    std::map<uint8_t, PendingRequest> pending_requests_;
    std::mutex pending_requests_mutex_;

    std::thread timeout_thread_;
};

// ModbusRtuProtocol implementation
ModbusRtuProtocol::ModbusRtuProtocol() : pImpl_(std::make_unique<Impl>()) {}

ModbusRtuProtocol::~ModbusRtuProtocol() = default;

bool ModbusRtuProtocol::initialize(std::shared_ptr<driver::ICommunicationDriver> driver) {
    return pImpl_->initialize(driver);
}

void ModbusRtuProtocol::shutdown() {
    pImpl_->shutdown();
}

bool ModbusRtuProtocol::isReady() const {
    return pImpl_->isReady();
}

common::AsyncResult ModbusRtuProtocol::readRegister(uint16_t address, uint8_t sub_address, uint32_t& data) {
    return pImpl_->readRegister(address, sub_address, data);
}

common::AsyncResult ModbusRtuProtocol::writeRegister(uint16_t address, uint8_t sub_address, uint32_t data) {
    return pImpl_->writeRegister(address, sub_address, data);
}

void ModbusRtuProtocol::readRegisterAsync(uint16_t address, uint8_t sub_address,
                                         common::AsyncCallback callback,
                                         const common::TimeoutConfig& timeout_config) {
    pImpl_->readRegisterAsync(address, sub_address, callback, timeout_config);
}

void ModbusRtuProtocol::writeRegisterAsync(uint16_t address, uint8_t sub_address, uint32_t data,
                                          common::AsyncCallback callback,
                                          const common::TimeoutConfig& timeout_config) {
    pImpl_->writeRegisterAsync(address, sub_address, data, callback, timeout_config);
}

std::string ModbusRtuProtocol::getProtocolType() const {
    return pImpl_->getProtocolType();
}

void ModbusRtuProtocol::setNodeId(uint8_t node_id) {
    pImpl_->setNodeId(node_id);
}

uint8_t ModbusRtuProtocol::getNodeId() const {
    return pImpl_->getNodeId();
}

} // namespace protocol
} // namespace sl_vcu_all
