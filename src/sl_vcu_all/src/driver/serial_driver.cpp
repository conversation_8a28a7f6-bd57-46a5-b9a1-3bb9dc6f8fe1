#include "sl_vcu_all/driver/communication_driver.hpp"
#include <fcntl.h>
#include <termios.h>
#include <unistd.h>
#include <thread>
#include <queue>
#include <mutex>
#include <condition_variable>
#include <atomic>
#include <chrono>
#include <cstring>

namespace sl_vcu_all {
namespace driver {

class SerialDriver::Impl {
public:
    Impl() : serial_fd_(-1), running_(false), initialized_(false) {}
    
    ~Impl() {
        shutdown();
    }

    bool initialize(const std::string& device_config) {
        if (initialized_) {
            return true;
        }

        // Parse config string (format: "/dev/ttyUSB0:9600:8N1")
        auto colon_pos1 = device_config.find(':');
        if (colon_pos1 == std::string::npos) {
            device_path_ = device_config;
            baud_rate_ = 9600;  // Default
        } else {
            device_path_ = device_config.substr(0, colon_pos1);
            auto colon_pos2 = device_config.find(':', colon_pos1 + 1);
            if (colon_pos2 == std::string::npos) {
                baud_rate_ = std::stoi(device_config.substr(colon_pos1 + 1));
            } else {
                baud_rate_ = std::stoi(device_config.substr(colon_pos1 + 1, colon_pos2 - colon_pos1 - 1));
                // Parse additional parameters if needed
            }
        }

        // Open serial port
        serial_fd_ = open(device_path_.c_str(), O_RDWR | O_NOCTTY | O_NDELAY);
        if (serial_fd_ < 0) {
            return false;
        }

        // Configure serial port
        struct termios options;
        if (tcgetattr(serial_fd_, &options) != 0) {
            close(serial_fd_);
            serial_fd_ = -1;
            return false;
        }

        // Set baud rate
        speed_t speed = B9600;  // Default
        switch (baud_rate_) {
            case 9600: speed = B9600; break;
            case 19200: speed = B19200; break;
            case 38400: speed = B38400; break;
            case 57600: speed = B57600; break;
            case 115200: speed = B115200; break;
            default: speed = B9600; break;
        }
        
        cfsetispeed(&options, speed);
        cfsetospeed(&options, speed);

        // Configure for 8N1
        options.c_cflag &= ~PARENB;   // No parity
        options.c_cflag &= ~CSTOPB;   // 1 stop bit
        options.c_cflag &= ~CSIZE;    // Clear data size bits
        options.c_cflag |= CS8;       // 8 data bits
        options.c_cflag |= CREAD | CLOCAL;  // Enable receiver, ignore modem control lines

        // Raw mode
        options.c_lflag &= ~(ICANON | ECHO | ECHOE | ISIG);
        options.c_iflag &= ~(IXON | IXOFF | IXANY);
        options.c_oflag &= ~OPOST;

        // Set timeouts
        options.c_cc[VMIN] = 0;   // Non-blocking read
        options.c_cc[VTIME] = 1;  // 0.1 second timeout

        if (tcsetattr(serial_fd_, TCSANOW, &options) != 0) {
            close(serial_fd_);
            serial_fd_ = -1;
            return false;
        }

        // Start threads
        running_ = true;
        receive_thread_ = std::thread(&Impl::receiveThreadFunction, this);
        send_thread_ = std::thread(&Impl::sendThreadFunction, this);
        
        initialized_ = true;
        return true;
    }

    void shutdown() {
        if (!initialized_) {
            return;
        }

        running_ = false;
        
        // Notify send thread
        {
            std::lock_guard<std::mutex> lock(send_queue_mutex_);
            send_queue_cv_.notify_all();
        }

        if (receive_thread_.joinable()) {
            receive_thread_.join();
        }
        
        if (send_thread_.joinable()) {
            send_thread_.join();
        }

        if (serial_fd_ >= 0) {
            close(serial_fd_);
            serial_fd_ = -1;
        }

        initialized_ = false;
    }

    bool isReady() const {
        return initialized_ && serial_fd_ >= 0;
    }

    common::AsyncResult sendFrame(const common::CommFrame& frame) {
        if (!isReady()) {
            return common::AsyncResult(common::ResultCode::DEVICE_NOT_READY, "Driver not ready");
        }

        // For Modbus RTU, the frame data contains the complete Modbus frame
        ssize_t bytes_sent = write(serial_fd_, frame.data, frame.length);
        if (bytes_sent != frame.length) {
            return common::AsyncResult(common::ResultCode::COMMUNICATION_ERROR, "Failed to send serial frame");
        }

        // Flush output
        tcdrain(serial_fd_);

        return common::AsyncResult(common::ResultCode::SUCCESS);
    }

    void sendFrameAsync(const common::CommFrame& frame, 
                       common::AsyncCallback callback,
                       const common::TimeoutConfig& timeout_config) {
        if (!isReady()) {
            if (callback) {
                callback(common::AsyncResult(common::ResultCode::DEVICE_NOT_READY, "Driver not ready"));
            }
            return;
        }

        // Queue frame for sending
        {
            std::lock_guard<std::mutex> lock(send_queue_mutex_);
            SendRequest request;
            request.frame = frame;
            request.callback = callback;
            request.timeout = timeout_config.timeout;
            request.timestamp = std::chrono::steady_clock::now();
            send_queue_.push(request);
        }
        send_queue_cv_.notify_one();
    }

    void setReceiveCallback(std::function<void(const common::CommFrame&)> callback) {
        std::lock_guard<std::mutex> lock(receive_callback_mutex_);
        receive_callback_ = callback;
    }

    std::string getDriverType() const {
        return "Serial";
    }

    std::string getConfiguration() const {
        return device_path_ + ":" + std::to_string(baud_rate_);
    }

private:
    struct SendRequest {
        common::CommFrame frame;
        common::AsyncCallback callback;
        std::chrono::milliseconds timeout;
        std::chrono::steady_clock::time_point timestamp;
    };

    void receiveThreadFunction() {
        uint8_t buffer[256];
        size_t buffer_pos = 0;
        
        while (running_) {
            uint8_t byte;
            ssize_t bytes_read = read(serial_fd_, &byte, 1);
            
            if (bytes_read == 1) {
                buffer[buffer_pos++] = byte;
                
                // Simple frame detection for Modbus RTU
                // In a real implementation, you would need proper frame parsing
                if (buffer_pos >= 4) {  // Minimum Modbus frame size
                    // Check if we have a complete frame
                    // This is a simplified implementation
                    if (buffer_pos >= 8 || 
                        (buffer_pos >= 4 && std::chrono::steady_clock::now() - last_byte_time_ > std::chrono::milliseconds(10))) {
                        
                        common::CommFrame frame;
                        frame.length = buffer_pos;
                        memcpy(frame.data, buffer, std::min(buffer_pos, sizeof(frame.data)));
                        
                        std::lock_guard<std::mutex> lock(receive_callback_mutex_);
                        if (receive_callback_) {
                            receive_callback_(frame);
                        }
                        
                        buffer_pos = 0;
                    }
                }
                last_byte_time_ = std::chrono::steady_clock::now();
            } else if (bytes_read < 0 && running_) {
                // Error occurred, but only log if we're still supposed to be running
                std::this_thread::sleep_for(std::chrono::milliseconds(10));
            } else {
                // Timeout or no data
                std::this_thread::sleep_for(std::chrono::milliseconds(1));
            }
        }
    }

    void sendThreadFunction() {
        while (running_) {
            std::unique_lock<std::mutex> lock(send_queue_mutex_);
            send_queue_cv_.wait(lock, [this] { return !send_queue_.empty() || !running_; });

            if (!running_) {
                break;
            }

            if (!send_queue_.empty()) {
                SendRequest request = send_queue_.front();
                send_queue_.pop();
                lock.unlock();

                // Check timeout
                auto now = std::chrono::steady_clock::now();
                auto elapsed = std::chrono::duration_cast<std::chrono::milliseconds>(now - request.timestamp);
                
                if (elapsed > request.timeout) {
                    if (request.callback) {
                        request.callback(common::AsyncResult(common::ResultCode::TIMEOUT, "Send timeout"));
                    }
                    continue;
                }

                // Send frame
                auto result = sendFrame(request.frame);
                if (request.callback) {
                    request.callback(result);
                }
            }
        }
    }

    int serial_fd_;
    std::string device_path_;
    int baud_rate_;
    std::atomic<bool> running_;
    std::atomic<bool> initialized_;
    
    std::thread receive_thread_;
    std::thread send_thread_;
    
    std::queue<SendRequest> send_queue_;
    std::mutex send_queue_mutex_;
    std::condition_variable send_queue_cv_;
    
    std::function<void(const common::CommFrame&)> receive_callback_;
    std::mutex receive_callback_mutex_;
    
    std::chrono::steady_clock::time_point last_byte_time_;
};

// SerialDriver implementation
SerialDriver::SerialDriver() : pImpl_(std::make_unique<Impl>()) {}

SerialDriver::~SerialDriver() = default;

bool SerialDriver::initialize(const std::string& config) {
    return pImpl_->initialize(config);
}

void SerialDriver::shutdown() {
    pImpl_->shutdown();
}

bool SerialDriver::isReady() const {
    return pImpl_->isReady();
}

common::AsyncResult SerialDriver::sendFrame(const common::CommFrame& frame) {
    return pImpl_->sendFrame(frame);
}

void SerialDriver::sendFrameAsync(const common::CommFrame& frame, 
                                 common::AsyncCallback callback,
                                 const common::TimeoutConfig& timeout_config) {
    pImpl_->sendFrameAsync(frame, callback, timeout_config);
}

void SerialDriver::setReceiveCallback(std::function<void(const common::CommFrame&)> callback) {
    pImpl_->setReceiveCallback(callback);
}

std::string SerialDriver::getDriverType() const {
    return pImpl_->getDriverType();
}

std::string SerialDriver::getConfiguration() const {
    return pImpl_->getConfiguration();
}

} // namespace driver
} // namespace sl_vcu_all
