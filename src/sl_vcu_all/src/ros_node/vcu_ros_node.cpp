#include "sl_vcu_all/ros_node/vcu_ros_node.hpp"
#include "sl_vcu_all/driver/communication_driver.hpp"
#include "sl_vcu_all/protocol/communication_protocol.hpp"
#include "sl_vcu_all/device/zl_motor_controller.hpp"
#include "sl_vcu_all/common/config.hpp"
#include <tf2/LinearMath/Quaternion.h>
#include <tf2_geometry_msgs/tf2_geometry_msgs.hpp>

namespace sl_vcu_all {
namespace ros_node {

VcuRosNode::VcuRosNode(const rclcpp::NodeOptions& options)
    : Node("vcu_ros_node", options) {
    
    RCLCPP_INFO(this->get_logger(), "Initializing VCU ROS Node");
    
    // Initialize parameters and configuration
    initializeParameters();
    loadConfiguration();
    
    // Initialize devices and application
    if (!initializeDevices()) {
        RCLCPP_ERROR(this->get_logger(), "Failed to initialize devices");
        return;
    }
    
    if (!initializeApplication()) {
        RCLCPP_ERROR(this->get_logger(), "Failed to initialize application");
        return;
    }
    
    // Create publishers
    odom_pub_ = this->create_publisher<nav_msgs::msg::Odometry>(config_.odom_topic, 10);
    motor_info_pub_ = this->create_publisher<sl_vcu_all::msg::MotorInfo>(config_.motor_info_topic, 10);
    motor_state_pub_ = this->create_publisher<sl_vcu_all::msg::MotorState>(config_.motor_state_topic, 10);
    power_off_pub_ = this->create_publisher<std_msgs::msg::Bool>(config_.power_off_topic, 10);
    joint_state_pub_ = this->create_publisher<sensor_msgs::msg::JointState>(config_.joint_state_topic, 10);
    
    // Create CAN TX publisher if not using direct communication
    if (config_.driver_type != "socket_can") {
        can_tx_pub_ = this->create_publisher<sl_vcu_all::msg::CanFrame>(config_.can_tx_topic, 10);
    }
    
    // Create subscribers
    cmd_vel_sub_ = this->create_subscription<geometry_msgs::msg::Twist>(
        config_.cmd_vel_topic, 10,
        std::bind(&VcuRosNode::cmdVelCallback, this, std::placeholders::_1));
    
    bumper_sub_ = this->create_subscription<sl_vcu_all::msg::BumperState>(
        config_.bumper_topic, 10,
        std::bind(&VcuRosNode::bumperCallback, this, std::placeholders::_1));
    
    filtered_imu_sub_ = this->create_subscription<sensor_msgs::msg::Imu>(
        config_.filtered_imu_topic, 10,
        std::bind(&VcuRosNode::filteredImuCallback, this, std::placeholders::_1));
    
    alarm_clear_sub_ = this->create_subscription<std_msgs::msg::Bool>(
        "clear_alarm", 10,
        std::bind(&VcuRosNode::clearAlarmCallback, this, std::placeholders::_1));
    
    // Create CAN RX subscriber if not using direct communication
    if (config_.driver_type != "socket_can") {
        can_rx_sub_ = this->create_subscription<sl_vcu_all::msg::CanFrame>(
            config_.can_rx_topic, 10,
            std::bind(&VcuRosNode::canRxCallback, this, std::placeholders::_1));
    }
    
    // Create services
    led_control_service_ = this->create_service<sl_vcu_all::srv::LedControl>(
        "led_control",
        std::bind(&VcuRosNode::ledControlServiceCallback, this, 
                  std::placeholders::_1, std::placeholders::_2));
    
    // Create TF broadcaster if enabled
    if (config_.publish_tf) {
        tf_broadcaster_ = std::make_unique<tf2_ros::TransformBroadcaster>(this);
    }
    
    // Create timers
    control_timer_ = this->create_wall_timer(
        config_.control_cycle,
        std::bind(&VcuRosNode::controlTimerCallback, this));
    
    status_timer_ = this->create_wall_timer(
        config_.status_update_cycle,
        std::bind(&VcuRosNode::statusTimerCallback, this));
    
    // Set up application callbacks
    if (application_) {
        application_->setStatusUpdateCallback(
            std::bind(&VcuRosNode::onStatusUpdate, this, std::placeholders::_1));
        application_->setOdometryUpdateCallback(
            std::bind(&VcuRosNode::onOdometryUpdate, this, std::placeholders::_1));
        application_->setPowerStatusUpdateCallback(
            std::bind(&VcuRosNode::onPowerStatusUpdate, this, std::placeholders::_1));
        application_->setErrorCallback(
            std::bind(&VcuRosNode::onApplicationError, this, std::placeholders::_1));
    }
    
    // Initialize timing
    last_cmd_vel_time_ = this->now();
    last_bumper_time_ = this->now();
    last_odom_time_ = this->now();
    
    RCLCPP_INFO(this->get_logger(), "VCU ROS Node initialized successfully");
}

VcuRosNode::~VcuRosNode() {
    if (application_) {
        application_->shutdown();
    }
}

void VcuRosNode::initializeParameters() {
    // Declare all parameters with default values
    this->declare_parameter("driver_type", "socket_can");
    this->declare_parameter("driver_config", "can0");
    this->declare_parameter("protocol_type", "canopen_sdo");
    this->declare_parameter("protocol_node_id", 1);
    
    this->declare_parameter("motor_device_type", "zl_motor");
    this->declare_parameter("led_device_type", "");
    this->declare_parameter("power_device_type", "");
    
    // Robot physical parameters
    this->declare_parameter("wheel_diameter_left", 0.140);
    this->declare_parameter("wheel_diameter_right", 0.140);
    this->declare_parameter("wheel_separation", 0.390);
    this->declare_parameter("gear_ratio", 1.0);
    this->declare_parameter("encoder_resolution", 16384.0);
    
    // Frame IDs
    this->declare_parameter("odom_frame_id", "odom");
    this->declare_parameter("base_frame_id", "base_link");
    this->declare_parameter("publish_tf", true);
    
    // Topic names
    this->declare_parameter("cmd_vel_topic", "cmd_vel");
    this->declare_parameter("odom_topic", "odom");
    this->declare_parameter("filtered_odom_topic", "");
    this->declare_parameter("joint_state_topic", "joint_state");
    this->declare_parameter("can_tx_topic", "can_tx");
    this->declare_parameter("can_rx_topic", "");
    this->declare_parameter("motor_info_topic", "motor_info");
    this->declare_parameter("motor_state_topic", "motor_state");
    this->declare_parameter("bumper_topic", "bumper_state");
    this->declare_parameter("power_off_topic", "power_off");
    this->declare_parameter("filtered_imu_topic", "sl_vcu_all/imu_data_filtered");
    
    // Control timing
    this->declare_parameter("control_cycle_ms", 20);
    this->declare_parameter("status_update_cycle_ms", 1000);
    this->declare_parameter("print_status_out", false);
    
    // Timeout parameters
    this->declare_parameter("cmd_vel_timeout_ms", 500);
    this->declare_parameter("bumper_timeout_ms", 5000);
    this->declare_parameter("sdo_response_timeout_ms", 1000);
    
    // Publishing options
    this->declare_parameter("publish_motor_info", false);
    
    // GPIO parameters
    this->declare_parameter("emergency_stop_bit0_trigger_level", 0);
    
    // Power off control parameters
    this->declare_parameter("power_off_timeout_ms", 5000);
    this->declare_parameter("power_off_publish_period_ms", 100);
    
    // IMU parameters
    this->declare_parameter("imu_time_offset_ms", 6);
}

void VcuRosNode::loadConfiguration() {
    // Load ROS node configuration
    config_.driver_type = this->get_parameter("driver_type").as_string();
    config_.driver_config = this->get_parameter("driver_config").as_string();
    config_.protocol_type = this->get_parameter("protocol_type").as_string();
    config_.protocol_node_id = this->get_parameter("protocol_node_id").as_int();
    
    config_.motor_device_type = this->get_parameter("motor_device_type").as_string();
    config_.led_device_type = this->get_parameter("led_device_type").as_string();
    config_.power_device_type = this->get_parameter("power_device_type").as_string();
    
    config_.odom_frame_id = this->get_parameter("odom_frame_id").as_string();
    config_.base_frame_id = this->get_parameter("base_frame_id").as_string();
    config_.publish_tf = this->get_parameter("publish_tf").as_bool();
    
    config_.cmd_vel_topic = this->get_parameter("cmd_vel_topic").as_string();
    config_.odom_topic = this->get_parameter("odom_topic").as_string();
    config_.filtered_odom_topic = this->get_parameter("filtered_odom_topic").as_string();
    config_.joint_state_topic = this->get_parameter("joint_state_topic").as_string();
    config_.can_tx_topic = this->get_parameter("can_tx_topic").as_string();
    config_.motor_info_topic = this->get_parameter("motor_info_topic").as_string();
    config_.motor_state_topic = this->get_parameter("motor_state_topic").as_string();
    config_.bumper_topic = this->get_parameter("bumper_topic").as_string();
    config_.power_off_topic = this->get_parameter("power_off_topic").as_string();
    config_.filtered_imu_topic = this->get_parameter("filtered_imu_topic").as_string();
    
    // Set can_rx_topic based on protocol if not specified
    config_.can_rx_topic = this->get_parameter("can_rx_topic").as_string();
    if (config_.can_rx_topic.empty()) {
        config_.can_rx_topic = "can_rx_" + std::to_string(0x580 + config_.protocol_node_id);
    }
    
    config_.control_cycle = std::chrono::milliseconds(this->get_parameter("control_cycle_ms").as_int());
    config_.status_update_cycle = std::chrono::milliseconds(this->get_parameter("status_update_cycle_ms").as_int());
    config_.print_status = this->get_parameter("print_status_out").as_bool();
    config_.publish_motor_info = this->get_parameter("publish_motor_info").as_bool();
    
    // Load application configuration
    app_config_.robot_params.wheel_diameter_left = this->get_parameter("wheel_diameter_left").as_double();
    app_config_.robot_params.wheel_diameter_right = this->get_parameter("wheel_diameter_right").as_double();
    app_config_.robot_params.wheel_separation = this->get_parameter("wheel_separation").as_double();
    app_config_.robot_params.gear_ratio = this->get_parameter("gear_ratio").as_double();
    app_config_.robot_params.encoder_resolution = this->get_parameter("encoder_resolution").as_double();
    
    app_config_.cmd_vel_timeout = std::chrono::milliseconds(this->get_parameter("cmd_vel_timeout_ms").as_int());
    app_config_.bumper_timeout = std::chrono::milliseconds(this->get_parameter("bumper_timeout_ms").as_int());
    app_config_.status_update_interval = std::chrono::milliseconds(this->get_parameter("status_update_cycle_ms").as_int());
    app_config_.emergency_stop_trigger_level = this->get_parameter("emergency_stop_bit0_trigger_level").as_int();
    app_config_.power_off_timeout = std::chrono::milliseconds(this->get_parameter("power_off_timeout_ms").as_int());
    app_config_.power_off_publish_interval = std::chrono::milliseconds(this->get_parameter("power_off_publish_period_ms").as_int());
    
    imu_time_offset_ms_ = this->get_parameter("imu_time_offset_ms").as_int();
    
    RCLCPP_INFO(this->get_logger(), "Configuration loaded:");
    RCLCPP_INFO(this->get_logger(), "  Driver: %s (%s)", config_.driver_type.c_str(), config_.driver_config.c_str());
    RCLCPP_INFO(this->get_logger(), "  Protocol: %s (node %d)", config_.protocol_type.c_str(), config_.protocol_node_id);
    RCLCPP_INFO(this->get_logger(), "  Motor device: %s", config_.motor_device_type.c_str());
    RCLCPP_INFO(this->get_logger(), "  Wheel separation: %.4f m", app_config_.robot_params.wheel_separation);
    RCLCPP_INFO(this->get_logger(), "  Control cycle: %ld ms", config_.control_cycle.count());
}

bool VcuRosNode::initializeDevices() {
    // Create communication driver
    driver_ = driver::CommunicationDriverFactory::createDriver(config_.driver_type);
    if (!driver_) {
        RCLCPP_ERROR(this->get_logger(), "Failed to create communication driver: %s", config_.driver_type.c_str());
        return false;
    }
    
    if (!driver_->initialize(config_.driver_config)) {
        RCLCPP_ERROR(this->get_logger(), "Failed to initialize communication driver");
        return false;
    }
    
    // Create communication protocol
    protocol_ = protocol::CommunicationProtocolFactory::createProtocol(config_.protocol_type);
    if (!protocol_) {
        RCLCPP_ERROR(this->get_logger(), "Failed to create communication protocol: %s", config_.protocol_type.c_str());
        return false;
    }
    
    protocol_->setNodeId(config_.protocol_node_id);
    
    if (!protocol_->initialize(driver_)) {
        RCLCPP_ERROR(this->get_logger(), "Failed to initialize communication protocol");
        return false;
    }
    
    // Create motor controller
    if (config_.motor_device_type == "zl_motor") {
        motor_controller_ = device::ZlDeviceFactory::createMotorController();
    } else {
        RCLCPP_ERROR(this->get_logger(), "Unknown motor device type: %s", config_.motor_device_type.c_str());
        return false;
    }
    
    if (!motor_controller_->initialize(protocol_, app_config_.robot_params)) {
        RCLCPP_ERROR(this->get_logger(), "Failed to initialize motor controller");
        return false;
    }
    
    // Set emergency stop trigger level for ZL motor controller
    if (auto zl_motor = std::dynamic_pointer_cast<device::ZlMotorController>(motor_controller_)) {
        zl_motor->setEmergencyStopTriggerLevel(app_config_.emergency_stop_trigger_level);
    }
    
    // Create power controller if specified
    if (!config_.power_device_type.empty()) {
        if (config_.power_device_type == "zl_power") {
            power_controller_ = device::ZlDeviceFactory::createPowerController();
            if (!power_controller_->initialize(protocol_)) {
                RCLCPP_WARN(this->get_logger(), "Failed to initialize power controller");
                power_controller_.reset();
            }
        }
    }
    
    // LED controller would be created here if needed
    
    RCLCPP_INFO(this->get_logger(), "Devices initialized successfully");
    return true;
}

bool VcuRosNode::initializeApplication() {
    application_ = std::make_unique<application::VcuApplication>();
    
    if (!application_->initialize(app_config_, motor_controller_, led_controller_, power_controller_)) {
        RCLCPP_ERROR(this->get_logger(), "Failed to initialize application");
        return false;
    }
    
    RCLCPP_INFO(this->get_logger(), "Application initialized successfully");
    return true;
}

// ROS callback methods
void VcuRosNode::cmdVelCallback(const geometry_msgs::msg::Twist::SharedPtr msg) {
    if (!application_) {
        return;
    }

    application::VelocityCommand cmd(msg->linear.x, msg->angular.z);
    application_->processVelocityCommand(cmd);
    last_cmd_vel_time_ = this->now();
}

void VcuRosNode::bumperCallback(const sl_vcu_all::msg::BumperState::SharedPtr msg) {
    if (!application_) {
        return;
    }

    application::BumperState state(msg->front_bumper_triggered, msg->back_bumper_triggered);
    application_->processBumperState(state);
    last_bumper_time_ = this->now();
}

void VcuRosNode::filteredImuCallback(const sensor_msgs::msg::Imu::SharedPtr msg) {
    // Convert orientation quaternion to yaw
    double roll, pitch, yaw;
    tf2::Quaternion tf_quat;
    tf2::fromMsg(msg->orientation, tf_quat);
    tf_quat.normalize();
    tf2::Matrix3x3(tf_quat).getRPY(roll, pitch, yaw);

    // Normalize to [-pi, pi]
    yaw = normalizeAngle(yaw);

    // Buffer the yaw with timestamp
    {
        std::lock_guard<std::mutex> lock(imu_buffer_mutex_);
        ImuYawSample sample;
        sample.timestamp = this->now();
        sample.yaw = yaw;

        imu_yaw_buffer_.push_back(sample);

        // Keep buffer size reasonable (keep last 2 seconds of data assuming 100Hz IMU)
        const size_t max_buffer_size = 200;
        while (imu_yaw_buffer_.size() > max_buffer_size) {
            imu_yaw_buffer_.pop_front();
        }
    }
}

void VcuRosNode::clearAlarmCallback(const std_msgs::msg::Bool::SharedPtr msg) {
    if (!application_ || !msg->data) {
        return;
    }

    application_->requestAlarmClear();
    RCLCPP_INFO(this->get_logger(), "Alarm clear requested");
}

void VcuRosNode::canRxCallback(const sl_vcu_all::msg::CanFrame::SharedPtr msg) {
    // Convert ROS CAN frame to common frame format and forward to driver
    // This is only used when not using direct SocketCAN
    if (!driver_) {
        return;
    }

    common::CommFrame frame;
    frame.id = msg->id;
    frame.length = msg->dlc;
    frame.is_extended = msg->is_extended;
    frame.is_rtr = msg->is_rtr;
    std::copy(msg->data.begin(), msg->data.begin() + std::min(static_cast<size_t>(msg->dlc), sizeof(frame.data)), frame.data);

    // Simulate frame reception (in a real implementation, this would be handled by the driver)
    // For now, we'll just log it
    RCLCPP_DEBUG(this->get_logger(), "Received CAN frame: ID=0x%X, DLC=%d", frame.id, frame.length);
}

// Timer callbacks
void VcuRosNode::controlTimerCallback() {
    if (!application_) {
        return;
    }

    // Update application
    application_->update();
}

void VcuRosNode::statusTimerCallback() {
    if (!application_) {
        return;
    }

    // Get current motor status and publish
    auto motor_status = application_->getMotorStatus();

    if (config_.publish_motor_info) {
        publishMotorInfo(motor_status);
    }

    publishMotorState(motor_status);

    // Get current odometry and publish
    auto odom_data = application_->getOdometryData();
    publishOdometry(odom_data);
    publishJointState(odom_data);

    // Get power status and publish
    auto power_status = application_->getPowerStatus();
    publishPowerStatus(power_status);
}

// Service callbacks
void VcuRosNode::ledControlServiceCallback(
    const std::shared_ptr<sl_vcu_all::srv::LedControl::Request> request,
    std::shared_ptr<sl_vcu_all::srv::LedControl::Response> response) {

    if (!application_) {
        response->success = false;
        response->message = "Application not initialized";
        return;
    }

    common::LedCommand command(request->channel, request->brightness, request->color);
    application_->setLedCommand(command);

    response->success = true;
    response->message = "LED command sent";
}

// Application callbacks
void VcuRosNode::onStatusUpdate(const application::MotorStatus& status) {
    // This callback is called from the application when status is updated
    // We can use it for immediate publishing or logging
    if (config_.print_status) {
        RCLCPP_INFO(this->get_logger(), "Motor status updated: enabled=%s, alarm=0x%X",
                   status.motors_enabled ? "true" : "false", status.last_alarm);
    }
}

void VcuRosNode::onOdometryUpdate(const application::OdometryData& odom) {
    // This callback is called from the application when odometry is updated
    // We can use it for immediate publishing or TF broadcasting
    if (config_.publish_tf) {
        publishTransform(odom);
    }
}

void VcuRosNode::onPowerStatusUpdate(const common::PowerStatus& power_status) {
    // This callback is called from the application when power status is updated
    if (power_status.power_off_requested) {
        RCLCPP_WARN(this->get_logger(), "Power off requested!");
    }
}

void VcuRosNode::onApplicationError(const std::string& error_message) {
    RCLCPP_ERROR(this->get_logger(), "Application error: %s", error_message.c_str());
}

// Publishing methods
void VcuRosNode::publishOdometry(const application::OdometryData& odom) {
    auto msg = nav_msgs::msg::Odometry();
    msg.header.stamp = this->now();
    msg.header.frame_id = config_.odom_frame_id;
    msg.child_frame_id = config_.base_frame_id;

    // Set position
    msg.pose.pose.position.x = odom.x;
    msg.pose.pose.position.y = odom.y;
    msg.pose.pose.position.z = 0.0;

    // Set orientation
    msg.pose.pose.orientation = createQuaternionFromYaw(odom.theta);

    // Set velocity
    msg.twist.twist.linear.x = odom.linear_vel;
    msg.twist.twist.linear.y = 0.0;
    msg.twist.twist.linear.z = 0.0;
    msg.twist.twist.angular.x = 0.0;
    msg.twist.twist.angular.y = 0.0;
    msg.twist.twist.angular.z = odom.angular_vel;

    // Set covariance (simplified)
    std::fill(msg.pose.covariance.begin(), msg.pose.covariance.end(), 0.0);
    std::fill(msg.twist.covariance.begin(), msg.twist.covariance.end(), 0.0);
    msg.pose.covariance[0] = 0.1;   // x
    msg.pose.covariance[7] = 0.1;   // y
    msg.pose.covariance[35] = 0.1;  // yaw
    msg.twist.covariance[0] = 0.1;  // vx
    msg.twist.covariance[35] = 0.1; // vyaw

    odom_pub_->publish(msg);
}

void VcuRosNode::publishMotorInfo(const application::MotorStatus& status) {
    auto msg = sl_vcu_all::msg::MotorInfo();
    msg.header.stamp = this->now();

    // Convert currents from 0.1A units to A
    msg.left_current = status.currents.left_current / 10.0;
    msg.right_current = status.currents.right_current / 10.0;

    // Convert temperatures from 0.1°C units to °C
    msg.left_temp = status.temperatures.left_temp / 10.0;
    msg.right_temp = status.temperatures.right_temp / 10.0;
    msg.driver_temp = status.temperatures.driver_temp / 10.0;

    // Set encoder positions
    msg.left_pos_encoder = status.positions.left_encoder;
    msg.right_pos_encoder = status.positions.right_encoder;

    // Set alarm code
    msg.alarm_code = status.last_alarm;

    motor_info_pub_->publish(msg);
}

void VcuRosNode::publishMotorState(const application::MotorStatus& status) {
    auto msg = sl_vcu_all::msg::MotorState();
    msg.header.stamp = this->now();

    // Set brake and emergency status based on GPIO bits
    msg.brake_release = status.gpio_status.isBrakeReleased();
    msg.emergency_stop = status.gpio_status.isEmergencyStop();

    // Set motor state based on status and alarm
    if (status.last_alarm != 0) {
        msg.state = "error";
        msg.error_code = static_cast<int32_t>(status.last_alarm);

        // Format alarm code as hex string
        std::stringstream ss;
        ss << "Motor alarm detected: 0x" << std::hex << std::uppercase << status.last_alarm;
        msg.error_info = ss.str();
    } else if (status.motors_enabled) {
        msg.state = "running";
        msg.error_code = 0;
        msg.error_info = "";
    } else {
        msg.state = "stopped";
        msg.error_code = 0;
        msg.error_info = "Motors not enabled";
    }

    motor_state_pub_->publish(msg);
}

void VcuRosNode::publishJointState(const application::OdometryData& odom) {
    auto msg = sensor_msgs::msg::JointState();
    msg.header.stamp = this->now();
    msg.header.frame_id = config_.base_frame_id;

    // Add wheel joints
    msg.name.push_back("left_wheel_joint");
    msg.name.push_back("right_wheel_joint");

    // Calculate wheel positions from odometry (simplified)
    double left_wheel_pos = 0.0;  // Would be calculated from encoder data
    double right_wheel_pos = 0.0; // Would be calculated from encoder data

    msg.position.push_back(left_wheel_pos);
    msg.position.push_back(right_wheel_pos);

    // Calculate wheel velocities
    double wheel_radius_left = app_config_.robot_params.wheel_diameter_left / 2.0;
    double wheel_radius_right = app_config_.robot_params.wheel_diameter_right / 2.0;
    double wheel_separation = app_config_.robot_params.wheel_separation;

    double left_wheel_vel = (odom.linear_vel - odom.angular_vel * wheel_separation / 2.0) / wheel_radius_left;
    double right_wheel_vel = (odom.linear_vel + odom.angular_vel * wheel_separation / 2.0) / wheel_radius_right;

    msg.velocity.push_back(left_wheel_vel);
    msg.velocity.push_back(right_wheel_vel);

    joint_state_pub_->publish(msg);
}

void VcuRosNode::publishPowerStatus(const common::PowerStatus& power_status) {
    auto msg = std_msgs::msg::Bool();
    msg.data = power_status.power_off_requested;
    power_off_pub_->publish(msg);
}

void VcuRosNode::publishTransform(const application::OdometryData& odom) {
    if (!tf_broadcaster_) {
        return;
    }

    geometry_msgs::msg::TransformStamped transform;
    transform.header.stamp = this->now();
    transform.header.frame_id = config_.odom_frame_id;
    transform.child_frame_id = config_.base_frame_id;

    transform.transform.translation.x = odom.x;
    transform.transform.translation.y = odom.y;
    transform.transform.translation.z = 0.0;
    transform.transform.rotation = createQuaternionFromYaw(odom.theta);

    tf_broadcaster_->sendTransform(transform);
}

// Utility methods
double VcuRosNode::normalizeAngle(double angle) {
    while (angle > M_PI) angle -= 2.0 * M_PI;
    while (angle < -M_PI) angle += 2.0 * M_PI;
    return angle;
}

geometry_msgs::msg::Quaternion VcuRosNode::createQuaternionFromYaw(double yaw) {
    tf2::Quaternion q;
    q.setRPY(0, 0, yaw);
    return tf2::toMsg(q);
}

bool VcuRosNode::getImuYawAtTime(const rclcpp::Time& target_time, double& yaw) {
    std::lock_guard<std::mutex> lock(imu_buffer_mutex_);

    if (imu_yaw_buffer_.empty()) {
        return false;
    }

    // Find the closest sample to the target time
    auto closest_it = std::min_element(imu_yaw_buffer_.begin(), imu_yaw_buffer_.end(),
        [&target_time](const ImuYawSample& a, const ImuYawSample& b) {
            auto diff_a = std::abs((a.timestamp - target_time).nanoseconds());
            auto diff_b = std::abs((b.timestamp - target_time).nanoseconds());
            return diff_a < diff_b;
        });

    if (closest_it != imu_yaw_buffer_.end()) {
        yaw = closest_it->yaw;
        return true;
    }

    return false;
}

} // namespace ros_node
} // namespace sl_vcu_all
