#include <rclcpp/rclcpp.hpp>
#include "sl_vcu_all/ros_node/vcu_ros_node.hpp"
#include <memory>

int main(int argc, char** argv) {
    rclcpp::init(argc, argv);
    
    try {
        auto node = std::make_shared<sl_vcu_all::ros_node::VcuRosNode>();
        
        RCLCPP_INFO(node->get_logger(), "VCU Node started, spinning...");
        rclcpp::spin(node);
        
        RCLCPP_INFO(node->get_logger(), "VCU Node shutting down");
    } catch (const std::exception& e) {
        RCLCPP_ERROR(rclcpp::get_logger("vcu_main"), "Exception in main: %s", e.what());
        return 1;
    }
    
    rclcpp::shutdown();
    return 0;
}
