#include "sl_vcu_all/common/config.hpp"
#include <algorithm>

namespace sl_vcu_all {
namespace common {

void ConfigManager::loadFromMap(const std::map<std::string, ConfigValue>& config_map) {
    config_map_ = config_map;
}

bool ConfigManager::hasKey(const std::string& key) const {
    return config_map_.find(key) != config_map_.end();
}

std::vector<std::string> ConfigManager::getKeys() const {
    std::vector<std::string> keys;
    keys.reserve(config_map_.size());
    
    for (const auto& pair : config_map_) {
        keys.push_back(pair.first);
    }
    
    return keys;
}

ros_node::RosNodeConfig ConfigManager::createRosNodeConfig() const {
    ros_node::RosNodeConfig config;
    
    // Communication configuration
    config.driver_type = getValue<std::string>(config_keys::DRIVER_TYPE, "socket_can");
    config.driver_config = getValue<std::string>(config_keys::DRIVER_CONFIG, "can0");
    config.protocol_type = getValue<std::string>(config_keys::PROTOCOL_TYPE, "canopen_sdo");
    config.protocol_node_id = getValue<int>(config_keys::PROTOCOL_NODE_ID, 1);
    
    // Device types
    config.motor_device_type = getValue<std::string>(config_keys::MOTOR_DEVICE_TYPE, "zl_motor");
    config.led_device_type = getValue<std::string>(config_keys::LED_DEVICE_TYPE, "");
    config.power_device_type = getValue<std::string>(config_keys::POWER_DEVICE_TYPE, "");
    
    // Frame IDs
    config.odom_frame_id = getValue<std::string>(config_keys::ODOM_FRAME_ID, "odom");
    config.base_frame_id = getValue<std::string>(config_keys::BASE_FRAME_ID, "base_link");
    config.publish_tf = getValue<bool>(config_keys::PUBLISH_TF, true);
    
    // Topic names
    config.cmd_vel_topic = getValue<std::string>(config_keys::CMD_VEL_TOPIC, "cmd_vel");
    config.odom_topic = getValue<std::string>(config_keys::ODOM_TOPIC, "odom");
    config.filtered_odom_topic = getValue<std::string>(config_keys::FILTERED_ODOM_TOPIC, "");
    config.joint_state_topic = getValue<std::string>(config_keys::JOINT_STATE_TOPIC, "joint_state");
    config.can_tx_topic = getValue<std::string>(config_keys::CAN_TX_TOPIC, "can_tx");
    config.can_rx_topic = getValue<std::string>(config_keys::CAN_RX_TOPIC, "");
    config.motor_info_topic = getValue<std::string>(config_keys::MOTOR_INFO_TOPIC, "motor_info");
    config.motor_state_topic = getValue<std::string>(config_keys::MOTOR_STATE_TOPIC, "motor_state");
    config.bumper_topic = getValue<std::string>(config_keys::BUMPER_TOPIC, "bumper_state");
    config.power_off_topic = getValue<std::string>(config_keys::POWER_OFF_TOPIC, "power_off");
    config.filtered_imu_topic = getValue<std::string>(config_keys::FILTERED_IMU_TOPIC, "sl_vcu_all/imu_data_filtered");
    
    // Publishing options
    config.publish_motor_info = getValue<bool>(config_keys::PUBLISH_MOTOR_INFO, false);
    config.print_status = getValue<bool>(config_keys::PRINT_STATUS_OUT, false);
    
    // Control timing
    config.control_cycle = std::chrono::milliseconds(getValue<int>(config_keys::CONTROL_CYCLE_MS, 20));
    config.status_update_cycle = std::chrono::milliseconds(getValue<int>(config_keys::STATUS_UPDATE_CYCLE_MS, 1000));
    
    // Legacy compatibility - map old parameters to new architecture
    bool use_sockcan_direct = getValue<bool>(config_keys::USE_SOCKCAN_DIRECT, true);
    if (!use_sockcan_direct) {
        // If not using direct sockcan, use mock driver with ROS topics
        config.driver_type = "mock";
    }
    
    std::string can_interface = getValue<std::string>(config_keys::CAN_INTERFACE, "can0");
    if (config.driver_type == "socket_can") {
        config.driver_config = can_interface;
    }
    
    // Set CAN RX topic based on node ID if not specified
    if (config.can_rx_topic.empty()) {
        int can_id_rx = 0x580 + config.protocol_node_id;
        config.can_rx_topic = "can_rx_" + std::to_string(can_id_rx);
    }
    
    return config;
}

application::VcuConfig ConfigManager::createApplicationConfig() const {
    application::VcuConfig config;
    
    // Robot parameters
    config.robot_params = createRobotParameters();
    
    // Timeout parameters
    config.cmd_vel_timeout = std::chrono::milliseconds(getValue<int>(config_keys::CMD_VEL_TIMEOUT_MS, 500));
    config.bumper_timeout = std::chrono::milliseconds(getValue<int>(config_keys::BUMPER_TIMEOUT_MS, 5000));
    config.status_update_interval = std::chrono::milliseconds(getValue<int>(config_keys::STATUS_UPDATE_CYCLE_MS, 1000));
    
    // Safety parameters
    config.emergency_stop_trigger_level = getValue<int>(config_keys::EMERGENCY_STOP_BIT0_TRIGGER_LEVEL, 0);
    
    // Power control parameters
    config.power_off_timeout = std::chrono::milliseconds(getValue<int>(config_keys::POWER_OFF_TIMEOUT_MS, 5000));
    config.power_off_publish_interval = std::chrono::milliseconds(getValue<int>(config_keys::POWER_OFF_PUBLISH_PERIOD_MS, 100));
    
    return config;
}

RobotParameters ConfigManager::createRobotParameters() const {
    RobotParameters params;
    
    params.wheel_diameter_left = getValue<double>(config_keys::WHEEL_DIAMETER_LEFT, 0.140);
    params.wheel_diameter_right = getValue<double>(config_keys::WHEEL_DIAMETER_RIGHT, 0.140);
    params.wheel_separation = getValue<double>(config_keys::WHEEL_SEPARATION, 0.390);
    params.gear_ratio = getValue<double>(config_keys::GEAR_RATIO, 1.0);
    params.encoder_resolution = getValue<double>(config_keys::ENCODER_RESOLUTION, 16384.0);
    
    return params;
}

} // namespace common
} // namespace sl_vcu_all
