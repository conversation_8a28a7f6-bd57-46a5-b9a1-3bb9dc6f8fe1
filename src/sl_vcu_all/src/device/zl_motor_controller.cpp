#include "sl_vcu_all/device/zl_motor_controller.hpp"
#include <atomic>
#include <mutex>
#include <cmath>

namespace sl_vcu_all {
namespace device {

class ZlMotorController::Impl {
public:
    Impl() : initialized_(false), motors_enabled_(false), emergency_stop_trigger_level_(0) {}

    ~Impl() {
        shutdown();
    }

    bool initialize(std::shared_ptr<protocol::ICommunicationProtocol> protocol,
                   const common::RobotParameters& params) {
        if (initialized_) {
            return true;
        }

        protocol_ = protocol;
        robot_params_ = params;

        if (!protocol_ || !protocol_->isReady()) {
            return false;
        }

        initialized_ = true;
        return true;
    }

    void shutdown() {
        if (!initialized_) {
            return;
        }

        // Disable motors before shutdown
        if (motors_enabled_) {
            disableMotors([](const common::AsyncResult&) {}, {});
        }

        protocol_.reset();
        initialized_ = false;
    }

    bool isReady() const {
        return initialized_ && protocol_ && protocol_->isReady();
    }

    void enableMotors(common::AsyncCallback callback, const common::TimeoutConfig& timeout_config) {
        if (!isReady()) {
            if (callback) {
                callback(common::AsyncResult(common::ResultCode::DEVICE_NOT_READY, "Device not ready"));
            }
            return;
        }

        // Multi-step motor enable process
        enableMotorsStep1(callback, timeout_config);
    }

    void disableMotors(common::AsyncCallback callback, const common::TimeoutConfig& timeout_config) {
        if (!isReady()) {
            if (callback) {
                callback(common::AsyncResult(common::ResultCode::DEVICE_NOT_READY, "Device not ready"));
            }
            return;
        }

        protocol_->writeRegisterAsync(
            zl_constants::INDEX_CTRL, 
            zl_constants::SUBINDEX_DEFAULT, 
            zl_constants::CTRL_RELEASE,
            [this, callback](const common::AsyncResult& result) {
                if (result.isSuccess()) {
                    motors_enabled_ = false;
                }
                if (callback) {
                    callback(result);
                }
            },
            timeout_config
        );
    }

    void setMotorSpeeds(const common::MotorSpeeds& speeds,
                       common::AsyncCallback callback,
                       const common::TimeoutConfig& timeout_config) {
        if (!isReady()) {
            if (callback) {
                callback(common::AsyncResult(common::ResultCode::DEVICE_NOT_READY, "Device not ready"));
            }
            return;
        }

        if (!motors_enabled_) {
            if (callback) {
                callback(common::AsyncResult(common::ResultCode::DEVICE_NOT_READY, "Motors not enabled"));
            }
            return;
        }

        // Convert RPM to encoder speed
        int32_t left_encoder_speed = rpmToEncoderSpeed(speeds.left_rpm);
        int32_t right_encoder_speed = rpmToEncoderSpeed(-speeds.right_rpm);  // Right motor is inverted

        // Combine speeds into single 32-bit value
        uint32_t combined_speed = (static_cast<uint32_t>(right_encoder_speed) << 16) | 
                                 static_cast<uint32_t>(left_encoder_speed & 0x0000FFFF);

        protocol_->writeRegisterAsync(
            zl_constants::INDEX_DEST_SPEED,
            zl_constants::SUBINDEX_L_R,
            combined_speed,
            callback,
            timeout_config
        );
    }

    void readMotorPositions(std::function<void(const common::AsyncResult&, const common::MotorPositions&)> callback,
                           const common::TimeoutConfig& timeout_config) {
        if (!isReady()) {
            if (callback) {
                callback(common::AsyncResult(common::ResultCode::DEVICE_NOT_READY, "Device not ready"), {});
            }
            return;
        }

        // Read left position
        protocol_->readRegisterAsync(
            zl_constants::INDEX_POS,
            zl_constants::SUBINDEX_LEFT,
            [this, callback, timeout_config](const common::AsyncResult& result) {
                if (!result.isSuccess()) {
                    if (callback) {
                        callback(result, {});
                    }
                    return;
                }

                int32_t left_pos = static_cast<int32_t>(result.data);

                // Read right position
                protocol_->readRegisterAsync(
                    zl_constants::INDEX_POS,
                    zl_constants::SUBINDEX_RIGHT,
                    [callback, left_pos](const common::AsyncResult& result2) {
                        if (!result2.isSuccess()) {
                            if (callback) {
                                callback(result2, {});
                            }
                            return;
                        }

                        int32_t right_pos = static_cast<int32_t>(result2.data);
                        common::MotorPositions positions(left_pos, right_pos);
                        
                        if (callback) {
                            callback(common::AsyncResult(common::ResultCode::SUCCESS), positions);
                        }
                    },
                    timeout_config
                );
            },
            timeout_config
        );
    }

    void readMotorSpeeds(std::function<void(const common::AsyncResult&, const common::MotorSpeeds&)> callback,
                        const common::TimeoutConfig& timeout_config) {
        if (!isReady()) {
            if (callback) {
                callback(common::AsyncResult(common::ResultCode::DEVICE_NOT_READY, "Device not ready"), {});
            }
            return;
        }

        protocol_->readRegisterAsync(
            zl_constants::INDEX_SPEED,
            zl_constants::SUBINDEX_L_R,
            [this, callback](const common::AsyncResult& result) {
                if (!result.isSuccess()) {
                    if (callback) {
                        callback(result, {});
                    }
                    return;
                }

                // Extract left and right speeds from combined value
                int16_t left_speed = static_cast<int16_t>(result.data & 0xFFFF);
                int16_t right_speed = static_cast<int16_t>(result.data >> 16);

                // Convert to RPM
                double left_rpm = encoderToRpm(left_speed);
                double right_rpm = encoderToRpm(-right_speed);  // Right motor is inverted

                common::MotorSpeeds speeds(left_rpm, right_rpm);
                
                if (callback) {
                    callback(common::AsyncResult(common::ResultCode::SUCCESS), speeds);
                }
            },
            timeout_config
        );
    }

    void readMotorCurrents(std::function<void(const common::AsyncResult&, const common::MotorCurrents&)> callback,
                          const common::TimeoutConfig& timeout_config) {
        if (!isReady()) {
            if (callback) {
                callback(common::AsyncResult(common::ResultCode::DEVICE_NOT_READY, "Device not ready"), {});
            }
            return;
        }

        // Read left current
        protocol_->readRegisterAsync(
            zl_constants::INDEX_CURRENT_FB,
            zl_constants::SUBINDEX_CURRENT_FB_LEFT,
            [this, callback, timeout_config](const common::AsyncResult& result) {
                if (!result.isSuccess()) {
                    if (callback) {
                        callback(result, {});
                    }
                    return;
                }

                int16_t left_current = static_cast<int16_t>(result.data);

                // Read right current
                protocol_->readRegisterAsync(
                    zl_constants::INDEX_CURRENT_FB,
                    zl_constants::SUBINDEX_CURRENT_FB_RIGHT,
                    [callback, left_current](const common::AsyncResult& result2) {
                        if (!result2.isSuccess()) {
                            if (callback) {
                                callback(result2, {});
                            }
                            return;
                        }

                        int16_t right_current = static_cast<int16_t>(result2.data);
                        common::MotorCurrents currents(left_current, right_current);
                        
                        if (callback) {
                            callback(common::AsyncResult(common::ResultCode::SUCCESS), currents);
                        }
                    },
                    timeout_config
                );
            },
            timeout_config
        );
    }

    void readMotorTemperatures(std::function<void(const common::AsyncResult&, const common::MotorTemperatures&)> callback,
                              const common::TimeoutConfig& timeout_config) {
        if (!isReady()) {
            if (callback) {
                callback(common::AsyncResult(common::ResultCode::DEVICE_NOT_READY, "Device not ready"), {});
            }
            return;
        }

        // Read left temperature
        protocol_->readRegisterAsync(
            zl_constants::INDEX_TEMPERATURE,
            zl_constants::SUBINDEX_TEMPERATURE_LEFT,
            [this, callback, timeout_config](const common::AsyncResult& result) {
                if (!result.isSuccess()) {
                    if (callback) {
                        callback(result, {});
                    }
                    return;
                }

                int16_t left_temp = static_cast<int16_t>(result.data);

                // Read right temperature
                protocol_->readRegisterAsync(
                    zl_constants::INDEX_TEMPERATURE,
                    zl_constants::SUBINDEX_TEMPERATURE_RIGHT,
                    [this, callback, timeout_config, left_temp](const common::AsyncResult& result2) {
                        if (!result2.isSuccess()) {
                            if (callback) {
                                callback(result2, {});
                            }
                            return;
                        }

                        int16_t right_temp = static_cast<int16_t>(result2.data);

                        // Read driver temperature
                        protocol_->readRegisterAsync(
                            zl_constants::INDEX_TEMPERATURE,
                            zl_constants::SUBINDEX_TEMPERATURE_DRIVER,
                            [callback, left_temp, right_temp](const common::AsyncResult& result3) {
                                if (!result3.isSuccess()) {
                                    if (callback) {
                                        callback(result3, {});
                                    }
                                    return;
                                }

                                int16_t driver_temp = static_cast<int16_t>(result3.data);
                                common::MotorTemperatures temps(left_temp, right_temp, driver_temp);
                                
                                if (callback) {
                                    callback(common::AsyncResult(common::ResultCode::SUCCESS), temps);
                                }
                            },
                            timeout_config
                        );
                    },
                    timeout_config
                );
            },
            timeout_config
        );
    }

    void readGpioStatus(std::function<void(const common::AsyncResult&, const common::GpioStatus&)> callback,
                       const common::TimeoutConfig& timeout_config) {
        if (!isReady()) {
            if (callback) {
                callback(common::AsyncResult(common::ResultCode::DEVICE_NOT_READY, "Device not ready"), {});
            }
            return;
        }

        protocol_->readRegisterAsync(
            zl_constants::INDEX_DIN_STATUS,
            zl_constants::SUBINDEX_DEFAULT,
            [callback](const common::AsyncResult& result) {
                if (!result.isSuccess()) {
                    if (callback) {
                        callback(result, {});
                    }
                    return;
                }

                common::GpioStatus gpio_status(result.data);
                
                if (callback) {
                    callback(common::AsyncResult(common::ResultCode::SUCCESS), gpio_status);
                }
            },
            timeout_config
        );
    }

    void readLastAlarm(std::function<void(const common::AsyncResult&, uint32_t)> callback,
                      const common::TimeoutConfig& timeout_config) {
        if (!isReady()) {
            if (callback) {
                callback(common::AsyncResult(common::ResultCode::DEVICE_NOT_READY, "Device not ready"), 0);
            }
            return;
        }

        protocol_->readRegisterAsync(
            zl_constants::INDEX_LAST_ALARM,
            zl_constants::SUBINDEX_LAST_ALARM,
            [callback](const common::AsyncResult& result) {
                if (callback) {
                    callback(result, result.data);
                }
            },
            timeout_config
        );
    }

    void clearAlarm(common::AsyncCallback callback, const common::TimeoutConfig& timeout_config) {
        if (!isReady()) {
            if (callback) {
                callback(common::AsyncResult(common::ResultCode::DEVICE_NOT_READY, "Device not ready"));
            }
            return;
        }

        protocol_->writeRegisterAsync(
            zl_constants::INDEX_CTRL,
            zl_constants::SUBINDEX_DEFAULT,
            zl_constants::CTRL_ALARM_CLEAR,
            callback,
            timeout_config
        );
    }

    std::string getDeviceType() const {
        return "ZL Motor Controller";
    }

    bool areMotorsEnabled() const {
        return motors_enabled_;
    }

    void setEmergencyStopTriggerLevel(int level) {
        emergency_stop_trigger_level_ = level;
    }

private:
    void enableMotorsStep1(common::AsyncCallback callback, const common::TimeoutConfig& timeout_config) {
        // Step 1: Set speed mode
        protocol_->writeRegisterAsync(
            zl_constants::INDEX_RUN_MODE,
            zl_constants::SUBINDEX_DEFAULT,
            zl_constants::RUN_MODE_SPEED,
            [this, callback, timeout_config](const common::AsyncResult& result) {
                if (!result.isSuccess()) {
                    if (callback) {
                        callback(result);
                    }
                    return;
                }
                enableMotorsStep2(callback, timeout_config);
            },
            timeout_config
        );
    }

    void enableMotorsStep2(common::AsyncCallback callback, const common::TimeoutConfig& timeout_config) {
        // Step 2: Release motors
        protocol_->writeRegisterAsync(
            zl_constants::INDEX_CTRL,
            zl_constants::SUBINDEX_DEFAULT,
            zl_constants::CTRL_RELEASE,
            [this, callback, timeout_config](const common::AsyncResult& result) {
                if (!result.isSuccess()) {
                    if (callback) {
                        callback(result);
                    }
                    return;
                }
                enableMotorsStep3(callback, timeout_config);
            },
            timeout_config
        );
    }

    void enableMotorsStep3(common::AsyncCallback callback, const common::TimeoutConfig& timeout_config) {
        // Step 3: Enable motors
        protocol_->writeRegisterAsync(
            zl_constants::INDEX_CTRL,
            zl_constants::SUBINDEX_DEFAULT,
            zl_constants::CTRL_ENABLE,
            [this, callback, timeout_config](const common::AsyncResult& result) {
                if (!result.isSuccess()) {
                    if (callback) {
                        callback(result);
                    }
                    return;
                }
                enableMotorsStep4(callback, timeout_config);
            },
            timeout_config
        );
    }

    void enableMotorsStep4(common::AsyncCallback callback, const common::TimeoutConfig& timeout_config) {
        // Step 4: Enable speed control
        protocol_->writeRegisterAsync(
            zl_constants::INDEX_CTRL,
            zl_constants::SUBINDEX_DEFAULT,
            zl_constants::CTRL_SPEED_ENABLE,
            [this, callback](const common::AsyncResult& result) {
                if (result.isSuccess()) {
                    motors_enabled_ = true;
                }
                if (callback) {
                    callback(result);
                }
            },
            timeout_config
        );
    }

    int32_t rpmToEncoderSpeed(double rpm) {
        // Convert RPM to encoder speed based on robot parameters
        double rad_per_sec = rpm * M_PI / 30.0;
        double encoder_speed = rad_per_sec * robot_params_.encoder_resolution / (2.0 * M_PI);
        return static_cast<int32_t>(encoder_speed);
    }

    double encoderToRpm(int32_t encoder_speed) {
        // Convert encoder speed to RPM
        double rad_per_sec = encoder_speed * 2.0 * M_PI / robot_params_.encoder_resolution;
        return rad_per_sec * 30.0 / M_PI;
    }

    std::shared_ptr<protocol::ICommunicationProtocol> protocol_;
    common::RobotParameters robot_params_;
    std::atomic<bool> initialized_;
    std::atomic<bool> motors_enabled_;
    int emergency_stop_trigger_level_;
};

// ZlMotorController implementation
ZlMotorController::ZlMotorController() : pImpl_(std::make_unique<Impl>()) {}

ZlMotorController::~ZlMotorController() = default;

bool ZlMotorController::initialize(std::shared_ptr<protocol::ICommunicationProtocol> protocol,
                                  const common::RobotParameters& params) {
    return pImpl_->initialize(protocol, params);
}

void ZlMotorController::shutdown() {
    pImpl_->shutdown();
}

bool ZlMotorController::isReady() const {
    return pImpl_->isReady();
}

void ZlMotorController::enableMotors(common::AsyncCallback callback,
                                    const common::TimeoutConfig& timeout_config) {
    pImpl_->enableMotors(callback, timeout_config);
}

void ZlMotorController::disableMotors(common::AsyncCallback callback,
                                     const common::TimeoutConfig& timeout_config) {
    pImpl_->disableMotors(callback, timeout_config);
}

void ZlMotorController::setMotorSpeeds(const common::MotorSpeeds& speeds,
                                      common::AsyncCallback callback,
                                      const common::TimeoutConfig& timeout_config) {
    pImpl_->setMotorSpeeds(speeds, callback, timeout_config);
}

void ZlMotorController::readMotorPositions(std::function<void(const common::AsyncResult&, const common::MotorPositions&)> callback,
                                          const common::TimeoutConfig& timeout_config) {
    pImpl_->readMotorPositions(callback, timeout_config);
}

void ZlMotorController::readMotorSpeeds(std::function<void(const common::AsyncResult&, const common::MotorSpeeds&)> callback,
                                       const common::TimeoutConfig& timeout_config) {
    pImpl_->readMotorSpeeds(callback, timeout_config);
}

void ZlMotorController::readMotorCurrents(std::function<void(const common::AsyncResult&, const common::MotorCurrents&)> callback,
                                         const common::TimeoutConfig& timeout_config) {
    pImpl_->readMotorCurrents(callback, timeout_config);
}

void ZlMotorController::readMotorTemperatures(std::function<void(const common::AsyncResult&, const common::MotorTemperatures&)> callback,
                                             const common::TimeoutConfig& timeout_config) {
    pImpl_->readMotorTemperatures(callback, timeout_config);
}

void ZlMotorController::readGpioStatus(std::function<void(const common::AsyncResult&, const common::GpioStatus&)> callback,
                                      const common::TimeoutConfig& timeout_config) {
    pImpl_->readGpioStatus(callback, timeout_config);
}

void ZlMotorController::readLastAlarm(std::function<void(const common::AsyncResult&, uint32_t)> callback,
                                     const common::TimeoutConfig& timeout_config) {
    pImpl_->readLastAlarm(callback, timeout_config);
}

void ZlMotorController::clearAlarm(common::AsyncCallback callback,
                                  const common::TimeoutConfig& timeout_config) {
    pImpl_->clearAlarm(callback, timeout_config);
}

std::string ZlMotorController::getDeviceType() const {
    return pImpl_->getDeviceType();
}

bool ZlMotorController::areMotorsEnabled() const {
    return pImpl_->areMotorsEnabled();
}

void ZlMotorController::setEmergencyStopTriggerLevel(int level) {
    pImpl_->setEmergencyStopTriggerLevel(level);
}

// ZlPowerController implementation
class ZlPowerController::Impl {
public:
    Impl() : initialized_(false) {}

    ~Impl() {
        shutdown();
    }

    bool initialize(std::shared_ptr<protocol::ICommunicationProtocol> protocol) {
        if (initialized_) {
            return true;
        }

        protocol_ = protocol;

        if (!protocol_ || !protocol_->isReady()) {
            return false;
        }

        initialized_ = true;
        return true;
    }

    void shutdown() {
        if (!initialized_) {
            return;
        }

        protocol_.reset();
        initialized_ = false;
    }

    bool isReady() const {
        return initialized_ && protocol_ && protocol_->isReady();
    }

    void controlPowerButtonLight(bool enable,
                                common::AsyncCallback callback,
                                const common::TimeoutConfig& timeout_config) {
        if (!isReady()) {
            if (callback) {
                callback(common::AsyncResult(common::ResultCode::DEVICE_NOT_READY, "Device not ready"));
            }
            return;
        }

        uint32_t gpio_value = enable ? zl_constants::GPIO_O_POWER_OFF_BUTTON_LIGHT : 0;

        protocol_->writeRegisterAsync(
            zl_constants::INDEX_DOUT_CONTROL,
            zl_constants::SUBINDEX_DOUT_CONTROL,
            gpio_value,
            callback,
            timeout_config
        );
    }

    void controlMainPower(bool enable,
                         common::AsyncCallback callback,
                         const common::TimeoutConfig& timeout_config) {
        if (!isReady()) {
            if (callback) {
                callback(common::AsyncResult(common::ResultCode::DEVICE_NOT_READY, "Device not ready"));
            }
            return;
        }

        uint32_t gpio_value = enable ? zl_constants::GPIO_O_POWER_CONTROL : 0;

        protocol_->writeRegisterAsync(
            zl_constants::INDEX_DOUT_CONTROL,
            zl_constants::SUBINDEX_DOUT_CONTROL,
            gpio_value,
            callback,
            timeout_config
        );
    }

    std::string getDeviceType() const {
        return "ZL Power Controller";
    }

private:
    std::shared_ptr<protocol::ICommunicationProtocol> protocol_;
    std::atomic<bool> initialized_;
};

ZlPowerController::ZlPowerController() : pImpl_(std::make_unique<Impl>()) {}

ZlPowerController::~ZlPowerController() = default;

bool ZlPowerController::initialize(std::shared_ptr<protocol::ICommunicationProtocol> protocol) {
    return pImpl_->initialize(protocol);
}

void ZlPowerController::shutdown() {
    pImpl_->shutdown();
}

bool ZlPowerController::isReady() const {
    return pImpl_->isReady();
}

void ZlPowerController::controlPowerButtonLight(bool enable,
                                               common::AsyncCallback callback,
                                               const common::TimeoutConfig& timeout_config) {
    pImpl_->controlPowerButtonLight(enable, callback, timeout_config);
}

void ZlPowerController::controlMainPower(bool enable,
                                        common::AsyncCallback callback,
                                        const common::TimeoutConfig& timeout_config) {
    pImpl_->controlMainPower(enable, callback, timeout_config);
}

std::string ZlPowerController::getDeviceType() const {
    return pImpl_->getDeviceType();
}

// ZlDeviceFactory implementation
std::unique_ptr<IMotorController> ZlDeviceFactory::createMotorController() {
    return std::make_unique<ZlMotorController>();
}

std::unique_ptr<IPowerController> ZlDeviceFactory::createPowerController() {
    return std::make_unique<ZlPowerController>();
}

} // namespace device
} // namespace sl_vcu_all
