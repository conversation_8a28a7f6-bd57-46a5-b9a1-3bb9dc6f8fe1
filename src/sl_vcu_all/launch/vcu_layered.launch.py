#!/usr/bin/env python3

import os
from launch import LaunchDescription
from launch.actions import DeclareLaunchArgument, OpaqueFunction
from launch.substitutions import LaunchConfiguration, PathJoinSubstitution
from launch_ros.actions import Node
from launch_ros.substitutions import FindPackageShare
from ament_index_python.packages import get_package_share_directory


def generate_launch_description():
    """Generate launch description for the layered VCU architecture."""
    
    # Declare launch arguments
    config_file_arg = DeclareLaunchArgument(
        'config_file',
        default_value='vcu_layered_config.yaml',
        description='Configuration file name'
    )
    
    driver_type_arg = DeclareLaunchArgument(
        'driver_type',
        default_value='socket_can',
        description='Communication driver type (socket_can, serial, mock)'
    )
    
    driver_config_arg = DeclareLaunchArgument(
        'driver_config',
        default_value='can0',
        description='Driver configuration (interface name for CAN, device path for serial)'
    )
    
    protocol_type_arg = DeclareLaunchArgument(
        'protocol_type',
        default_value='canopen_sdo',
        description='Communication protocol type (canopen_sdo, modbus_rtu, mock)'
    )
    
    protocol_node_id_arg = DeclareLaunchArgument(
        'protocol_node_id',
        default_value='1',
        description='Protocol node ID (CAN node ID or Modbus slave ID)'
    )
    
    motor_device_type_arg = DeclareLaunchArgument(
        'motor_device_type',
        default_value='zl_motor',
        description='Motor device type (zl_motor, mock)'
    )
    
    publish_tf_arg = DeclareLaunchArgument(
        'publish_tf',
        default_value='true',
        description='Whether to publish TF transforms'
    )
    
    publish_motor_info_arg = DeclareLaunchArgument(
        'publish_motor_info',
        default_value='false',
        description='Whether to publish detailed motor info'
    )
    
    def launch_setup(context, *args, **kwargs):
        """Setup function to create nodes with resolved parameters."""
        
        # Get package share directory
        pkg_share = get_package_share_directory('sl_vcu_all')
        
        # Resolve configuration file path
        config_file = LaunchConfiguration('config_file').perform(context)
        config_path = os.path.join(pkg_share, 'config', config_file)
        
        # Create VCU node with layered architecture
        vcu_node = Node(
            package='sl_vcu_all',
            executable='vcu_layered_node',  # This would be the new executable
            name='vcu_ros_node',
            output='screen',
            parameters=[
                config_path,
                {
                    # Override parameters from launch arguments
                    'driver_type': LaunchConfiguration('driver_type'),
                    'driver_config': LaunchConfiguration('driver_config'),
                    'protocol_type': LaunchConfiguration('protocol_type'),
                    'protocol_node_id': LaunchConfiguration('protocol_node_id'),
                    'motor_device_type': LaunchConfiguration('motor_device_type'),
                    'publish_tf': LaunchConfiguration('publish_tf'),
                    'publish_motor_info': LaunchConfiguration('publish_motor_info'),
                }
            ],
            remappings=[
                # Add any topic remappings here if needed
            ]
        )
        
        return [vcu_node]
    
    return LaunchDescription([
        config_file_arg,
        driver_type_arg,
        driver_config_arg,
        protocol_type_arg,
        protocol_node_id_arg,
        motor_device_type_arg,
        publish_tf_arg,
        publish_motor_info_arg,
        OpaqueFunction(function=launch_setup)
    ])


if __name__ == '__main__':
    generate_launch_description()
